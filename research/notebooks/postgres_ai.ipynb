pip install psycopg2-binary


POSTGRES_HOST = "database-1.cw7tvjq91kjw.us-east-1.rds.amazonaws.com"
POSTGRES_PASSWORD = "afri-Sam22*3"
POSTGRES_PORT = 5432
POSTGRES_USER = "afriex_samuel"

import psycopg2
import psycopg2.extras

# Create connection
conn = psycopg2.connect(
    host=POSTGRES_HOST,
    database="ai_copilot",  # Default database
    user=POSTGRES_USER,
    password=POSTGRES_PASSWORD,
    port=POSTGRES_PORT
)

# Create cursor
cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

# Get all schemas
cur.execute("""
    SELECT schema_name 
    FROM information_schema.schemata 
    WHERE schema_name NOT IN ('information_schema', 'pg_catalog')
    ORDER BY schema_name;
""")
schemas = cur.fetchall()

# Print schemas and their tables
for schema in schemas:
    schema_name = schema['schema_name']
    print(f"\nSchema: {schema_name}")
    print("-" * 50)
    
    # Get tables in this schema
    cur.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = %s
        ORDER BY table_name;
    """, (schema_name,))
    
    tables = cur.fetchall()
    for table in tables:
        print(f"- {table['table_name']}")

# Close cursor and connection
cur.close()
conn.close()






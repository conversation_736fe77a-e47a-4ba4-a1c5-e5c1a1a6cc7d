from app import create_app
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi import Request
import os
import uvicorn
from fastapi import APIRouter, HTTPException, BackgroundTasks,Request
from fastapi.responses import FileResponse
import os
from sql_copilot.handlers.feedback_manager import get_feedback_stats,save_feedback_and_execute_sql
from fastapi.templating import Jinja2Templates
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel,Field
from typing import Optional, Dict, Any, List
import pandas as pd
import json
import base64
from datetime import datetime, timedelta
import asyncio
import glob
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = create_app()

async def cleanup_old_temp_files():
    """Clean up CSV files older than 3 days from the temp_files directory"""
    try:
        temp_dir = "temp_files"
        if not os.path.exists(temp_dir):
            return

        # Calculate the cutoff time (3 days ago)
        cutoff_time = datetime.now() - timedelta(days=3)
        
        # Get all CSV files in the temp_files directory
        csv_files = glob.glob(os.path.join(temp_dir, "query_results_*.csv"))
        
        for file_path in csv_files:
            try:
                # Get file's last modification time
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                # If file is older than 3 days, delete it
                if file_mtime < cutoff_time:
                    os.remove(file_path)
                    logger.info(f"Deleted old file: {file_path}")
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")
                
    except Exception as e:
        logger.error(f"Error in cleanup_old_temp_files: {str(e)}")

async def schedule_cleanup():
    """Schedule the cleanup task to run every weekend"""
    while True:
        now = datetime.now()
        # Check if it's weekend (Saturday or Sunday)
        if now.weekday() >= 5:  # 5 is Saturday, 6 is Sunday
            await cleanup_old_temp_files()
            # Sleep until next day
            await asyncio.sleep(24 * 60 * 60)
        else:
            # Sleep until next day
            await asyncio.sleep(24 * 60 * 60)

@app.on_event("startup")
async def startup_event():
    """Start the cleanup scheduler when the application starts"""
    asyncio.create_task(schedule_cleanup())

app.mount("/static", StaticFiles(directory="static"), name="static")  

templates = Jinja2Templates(directory="templates")
@app.get("/", response_class=HTMLResponse)
async def get_query_page(request: Request):
    static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static/plots")
    try:
        for filename in os.listdir(static_dir):
            file_path = os.path.join(static_dir, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)
    except:
        pass
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/feedback", response_class=HTMLResponse)
async def get_feedback(request: Request):
    """Endpoint to retrieve all feedback and render the HTML page"""
    try:
        feedback_data = await get_feedback_stats()  # Ensure this is awaited
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_month = datetime.now().strftime("%B")  # Get the current month
        current_day = datetime.now().strftime("%A")  # Get the current day
        
        # Calculate the current week of the current month
        current_week_of_month = (datetime.now().day - 1) // 7 + 1  # Week number in the current month
        
        return templates.TemplateResponse(
            "feedback.html",
            {
                "request": request,
                "total_correct": feedback_data['total_correct'],
                "total_wrong": feedback_data['total_wrong'],
                "weekly_correct": feedback_data['weekly_correct'],
                "weekly_wrong": feedback_data['weekly_wrong'],
                "all_feedback": feedback_data["all_feedback"],
                "current_date": current_date,
                "current_week": current_week_of_month,
                "current_month": current_month,  # Pass current month to the template
                "current_day": current_day,  # Pass current day to the template

            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving feedback: {str(e)}")

@app.get("/health")
async def health_check():
    return {"status": "ok"}

@app.get("/training", response_class=HTMLResponse)
async def get_training_examples_page(request: Request):
        """Serve the training examples HTML page."""
        return templates.TemplateResponse("training_examples.html", {"request": request})


if __name__ == "__main__":
       uvicorn.run("main:app", host="0.0.0.0", port=5045, reload=True)



'''
gunicorn main:app \
  --worker-class uvicorn.workers.UvicornWorker \
  --workers 4 \
  --bind 0.0.0.0:5045 \
  --log-level info \
  --access-logfile - \
  --error-logfile - \
  --timeout 300 \
  --keep-alive 5 \
  --graceful-timeout 300
'''

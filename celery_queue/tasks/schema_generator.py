from ..celery_app import celery_app
from utils import logger
from sql_copilot.services.schema_generator.schema_analyzer import SchemaAnalyzer
from config import Config
from sql_copilot.services.schema_generator.backend.snowflake import SnowflakeSchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mongodb import MongoDBSchemaAnalyzer
import traceback

# You might want to use Redis or a database to store task status
task_status = {}  # This is just for demonstration. Use a proper database in production.

@celery_app.task(bind=True, max_retries=3,name="celery_queue.tasks.schema_generator.generate_schema_task")
def generate_schema_task(self, request_id: str, backend: str, connection_params: dict, optional_params: dict = None):
    """
    Background task for processing schema generation
    
    Args:
        request_id: Unique identifier for the request
        backend: Database backend name
        connection_params: Database connection parameters
        optional_params: Optional parameters for schema analysis (schemas, max_workers, etc.)
    """
    try:
        logger.info("Entering generate_schema_task...")
        logger.info(f"Task called with request_id: {request_id}, backend: {backend}")
        logger.info("Starting schema generation...")
        logger.info("Task execution confirmed.")
        
        # Update status to IN_PROGRESS
        task_status[request_id] = {
            "status": "IN_PROGRESS",
            "message": "Schema generation in progress"
        }
        
        logger.info("Initializing SchemaAnalyzer...")
        # Initialize the analyzer
        analyzer = SchemaAnalyzer(
            backend=backend,
            connection_params=connection_params,
            openai_api_key=Config.OPENAI_API_KEY
        )
        logger.info("SchemaAnalyzer initialized.")

        # Analyze the schema with optional parameters
        results, report, error = analyzer.analyze_schema(**(optional_params or {}))
        #analyzer.export_to_json(result_list=results,report=report)
        # Update status based on results
        if error:
            task_status[request_id] = {
                "status": "ERROR_OCCURRED",
                "message": f"Schema generation failed: {error}",
                "error_detail": error
            }
        else:
            task_status[request_id] = {
                "status": "COMPLETED",
                "message": "Schema generation completed successfully",
                "results": results,
                "report": report
            }
        
        logger.info(f"Completed schema generation for request_id: {request_id}")
        return task_status[request_id]
        
    except Exception as exc:
        error_msg = f"Schema generation failed: {str(exc)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        
        # Update status to ERROR
        task_status[request_id] = {
            "status": "ERROR_OCCURRED",
            "message": f"Schema generation failed: {str(exc)}",
            "error_detail": error_msg
        }
        


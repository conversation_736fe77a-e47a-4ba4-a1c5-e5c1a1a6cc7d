{"report": {"database": "dev", "total_collections": 84, "successfully_analyzed": 84, "failed_analysis": 0, "failed_collections": [], "execution_time": 514.17}, "collections": [{"database_name": "dev", "collection_name": "waitlists", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "referral-tiers", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "bvns", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "contacts", "description": "This collection stores information about individual contacts, including their unique identifiers and associated data. This table contains the following fields: _id, hash, contacts. The purpose of this collection is to manage and retrieve contact information efficiently, allowing for easy access and manipulation of contact data.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each contact, automatically generated by MongoDB."}, {"name": "hash", "data_type": "string", "is_categorical": false, "description": "A hashed representation of the contact's information, used for quick lookups and data integrity."}, {"name": "contacts", "data_type": "array", "is_categorical": false, "description": "An array of contact details, which may include names, phone numbers, email addresses, and other relevant information."}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"hash\": \"f1e2d3c4b5a69788abcdef**********abcdef**********abcdef12345678\",\n  \"contacts\": [\n    \"**********abcdef12345678\",\n    \"abcdef**********abcdef12\",\n    \"fedcba0********1abcdef12\"\n  ]\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "entities", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "feature-flags", "description": "This collection stores feature flags that control the availability of features in an application. Feature flags allow for toggling features on or off for specific users or groups, enabling controlled rollouts and A/B testing. This table contains the following fields: _id, key, value, userId, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each feature flag document, automatically generated by MongoDB."}, {"name": "key", "data_type": "string", "is_categorical": true, "description": "A string that represents the unique identifier for the feature flag.", "found_categorical_values": ["charge_method_selecton_style", "fee_application_style", "None", "charge_method_selection_style", "is_interac_enabled", "current_wallet_banner_url", "is_small_charge_enabled", "is_banner_enabled", "is_interac_send_enabled", "is_francophone_country", "can_view_korapay", "can_view_cards", "is_korapay_enabled", "review_prompt_style", "is_virtual_account_supported", "virtual_card_enabled", "is_banner_visible", "isInteracEnabled", "shouldShowBanner"]}, {"name": "value", "data_type": "string", "is_categorical": false, "description": "A boolean indicating whether the feature is enabled (true) or disabled (false)."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "An optional identifier for the user associated with the feature flag, allowing for user-specific feature toggling."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to track document revisions.", "found_categorical_values": ["0"]}, {"name": "key", "data_type": "string", "is_categorical": true, "description": "A string that represents the unique identifier for the feature flag.", "found_categorical_values": ["charge_method_selecton_style", "fee_application_style", "None", "charge_method_selection_style", "can_view_cards", "is_small_charge_enabled", "is_interac_enabled", "can_view_korapay", "is_interac_send_enabled", "current_wallet_banner_url", "is_francophone_country", "is_banner_enabled", "is_korapay_enabled", "review_prompt_style", "is_virtual_account_supported", "virtual_card_enabled", "is_banner_visible", "shouldShowBanner"]}, {"name": "value", "data_type": "string", "is_categorical": true, "description": "A boolean indicating whether the feature is enabled (true) or disabled (false).", "found_categorical_values": ["prompt-until-ach-selected", "active", "remove_fee_from_amount", "inactive", "None", "https://firebasestorage.googleapis.com/v0/b/afriex-7993d.firebasestorage.app/o/IMG_4020.png?alt=media&token=********-d3d7-4a04-b788-5cb02b7deb20", "trustpilot", "false", "[\"cm\",\"ci\",\"sn\"]", "{\"countries\":[\"za\",\"ke\"],\"currencies\":[]}", "{\"can_not_view_countries\":[\"ca\"],\"can_not_view_currencies\":[\"\"],\"should_see_bank_transfer_payment_currencies\":[\"eur\"]}", "[\"fr\",\"ie\",\"nl\",\"de\",\"be\",\"ng\",\"us\",\"gh\",\"ke\",\"ca\",\"cm\",\"eg\",\"in\",\"za\",\"ug\",\"rw\"]", "0", "{\"can_not_view_countries\":[\"ca\"],\"can_not_view_currencies\":[\"eur\"],\"should_see_bank_transfer_payment_currencies\":[\"eur\"]}", "add_fee_to_amount", "true", "{\"can_not_view_countries\":[\"\"]}", "{\"countries\":[\"gh\",\"ke\"],\"currencies\":[]}", "[\"fr\",\"ie\",\"nl\",\"de\",\"be\",\"ng\",\"us\",\"gh\",\"ke\",\"ca\",\"cm\",\"eg\",\"in\",\"za\",\"ug\"]"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"key\": \"payment_method_selection_style\",\n  \"value\": \"prompt-until-option-selected\",\n  \"userId\": \"**********abcdef12345678\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "mobilemoneyinfos", "description": "This collection stores information related to mobile money accounts, including user details, account status, and service provider information. This table contains the following fields: _id, phone, country, provider, user, type, deactivated, name, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "phone", "data_type": "string", "is_categorical": false, "description": "The phone number associated with the mobile money account."}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the mobile money service is being used.", "found_categorical_values": ["gh", "None", "ug", "ke"]}, {"name": "provider", "data_type": "string", "is_categorical": true, "description": "The name of the mobile money service provider.", "found_categorical_values": ["MTN", "None", "TIGO", "SAFARICOM", "MPS", "AIRTEL", "VODAFONE", "ZEEPAY"]}, {"name": "user", "data_type": "string", "is_categorical": false, "description": "The user associated with the mobile money account."}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of mobile money account (e.g., personal, business).", "found_categorical_values": ["payout", "None"]}, {"name": "deactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the account is deactivated.", "found_categorical_values": ["False", "True", "None"]}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The name of the account holder."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the document was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB for document versioning.", "found_categorical_values": ["0", "None"]}], "sample_documents": "{\n  \"_id\": \"73c9a1f473805d4cd78e153e\",\n  \"phone\": \"************\",\n  \"country\": \"ug\",\n  \"provider\": \"AIRTEL\",\n  \"user\": \"73b1c241eb5014e7dfe37955\",\n  \"type\": \"payout\",\n  \"deactivated\": false,\n  \"name\": \"AIRTEL UGANDA\",\n  \"createdAt\": \"2023-07-15T10:15:30.123000\",\n  \"updatedAt\": \"2023-07-15T10:15:30.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "businesses", "description": "This collection stores information about various businesses, including their identification, status, and timestamps for record management. This table contains the following fields: _id, name, countryCode, isDeactivated, isVerified, createdAt, updatedAt, __v. The purpose of this collection is to maintain a structured record of businesses for easy retrieval and management, facilitating operations such as verification and status tracking.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each business record, automatically generated by MongoDB."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The name of the business."}, {"name": "countryCode", "data_type": "string", "is_categorical": true, "description": "The ISO code representing the country where the business is located.", "found_categorical_values": ["US", "NG"]}, {"name": "isDeactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating whether the business is currently deactivated.", "found_categorical_values": ["False"]}, {"name": "isVerified", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating whether the business has been verified.", "found_categorical_values": ["False"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the business record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating the last time the business record was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"name\": \"ExampleCorp, llc\",\n  \"countryCode\": \"US\",\n  \"isDeactivated\": true,\n  \"isVerified\": true,\n  \"createdAt\": \"2025-02-15T09:30:00.000000\",\n  \"updatedAt\": \"2025-02-15T09:30:00.000000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "logs", "description": "This collection is used to store log entries related to user sessions and activities within the application. It helps in tracking user interactions and session management. This table contains the following fields: _id, sessionId, userId, expiresAt, logs, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each log entry, automatically generated by MongoDB."}, {"name": "sessionId", "data_type": "string", "is_categorical": false, "description": "A unique identifier for the user session associated with the log entry."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The unique identifier of the user who generated the log entry."}, {"name": "expiresAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the log entry should expire or be considered invalid."}, {"name": "logs", "data_type": "array", "is_categorical": false, "description": "An array of log messages or events recorded during the user session.", "array_items": [{"name": "logs[].message", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "logs[].meta", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "logs[].meta.sessionId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "logs[].meta.userId", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "logs[].count", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "logs[].timestamp", "data_type": "float", "is_categorical": false, "description": ""}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the log entry was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating the last time the log entry was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to manage document versioning.", "found_categorical_values": ["0"]}, {"name": "logs", "data_type": "array", "is_categorical": false, "description": "An array of log messages or events recorded during the user session.", "array_items": [{"name": "logs[].message", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "logs[].meta", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "logs[].meta.sessionId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "logs[].meta.userId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "logs[].meta.shouldSave", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "logs[].count", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "logs[].timestamp", "data_type": "float", "is_categorical": false, "description": ""}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"sessionId\": \"12345678-90ab-cdef-1234-567890abcdef\",\n  \"userId\": \"abcdef**********abcdef1234\",\n  \"expiresAt\": \"2025-06-30T14:45:10.123000\",\n  \"logs\": [\n    {\n      \"message\": \"Initiates authentication via ExampleService\",\n      \"meta\": {\n        \"sessionId\": \"12345678-90ab-cdef-1234-567890abcdef\",\n        \"userId\": \"abcdef**********abcdef1234\"\n      },\n      \"count\": 2,\n      \"timestamp\": 1747395182710.0\n    }\n  ],\n  \"createdAt\": \"2025-06-15T14:45:10.123000\",\n  \"updatedAt\": \"2025-06-15T14:45:10.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "rewards-cards", "description": "This collection stores information about reward cards issued to users, tracking their status and associated metadata. This table contains the following fields: _id, rewardId, hash, isDeactivated, __v, createdAt, updatedAt, redeemedBy. The purpose of this collection is to manage and maintain the lifecycle of reward cards, including their creation, updates, and redemption status.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each reward card document."}, {"name": "rewardId", "data_type": "string", "is_categorical": false, "description": "The identifier for the specific reward associated with the card."}, {"name": "hash", "data_type": "string", "is_categorical": false, "description": "A hashed value used for security purposes, often related to the card's unique attributes."}, {"name": "isDeactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating whether the reward card is deactivated.", "found_categorical_values": ["False", "True"]}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by Mon<PERSON>ose to track document revisions.", "found_categorical_values": ["0", "None"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the reward card was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating the last time the reward card was updated."}, {"name": "redeemed<PERSON><PERSON>", "data_type": "object", "is_categorical": false, "description": "The identifier of the user who redeemed the reward card, if applicable.", "nested_fields": [{"name": "redeemed<PERSON>y.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "redeemedBy.userId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "redeemedBy.rewardTier", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "gold"]}, {"name": "redeemedBy.redeemedAt", "data_type": "string", "is_categorical": false, "description": ""}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"rewardId\": \"b1c2d3e4f5g67890abcdef34\",\n  \"hash\": \"x9yZ12Bcd+RvmNnqv+oPjx==\",\n  \"isDeactivated\": false,\n  \"__v\": 0,\n  \"createdAt\": \"2025-07-15T14:30:12.123000\",\n  \"updatedAt\": \"2025-07-15T14:30:12.123000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "tiers", "description": "This collection stores information about different tiers available in the system, which can be used to manage user benefits, discounts, and access levels. This table contains the following fields: _id, name, discount, isActive, transactionCountThreshold, type, createdAt, updatedAt, __v, code, admin, allowedDestChannels, allowedSourceChannels, currencies.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each tier document."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The name of the tier, which identifies it within the system."}, {"name": "discount", "data_type": "float", "is_categorical": false, "description": "The discount percentage or amount associated with this tier."}, {"name": "isActive", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the tier is currently active.", "found_categorical_values": ["False", "True"]}, {"name": "transactionCountThreshold", "data_type": "integer", "is_categorical": false, "description": "The minimum number of transactions required to qualify for this tier."}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of tier, which may define its characteristics or usage.", "found_categorical_values": ["promotion", "None", "tier", "paymentMethod"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the tier was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the tier was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB for document versioning.", "found_categorical_values": ["0", "None"]}, {"name": "code", "data_type": "string", "is_categorical": true, "description": "A unique code associated with the tier for reference.", "found_categorical_values": ["None", "REGULATED_DEBIT", "NEW_USER", "REGULAR_USER", "UNREGULATED_DEBIT", "INACTIVE_USER", "CHURNED_USER", "ACH_BANK"]}, {"name": "admin", "data_type": "object", "is_categorical": false, "description": "The identifier of the admin who created or manages the tier.", "nested_fields": [{"name": "admin.id", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["622b244cc772b1120bd60141", "None"]}, {"name": "admin.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["operations.manager", "engineering.manager", "None", "finance.manager"]}]}, {"name": "allowedDestChannels", "data_type": "array", "is_categorical": false, "description": "An array of channels where this tier can be applied."}, {"name": "allowedSourceChannels", "data_type": "array", "is_categorical": false, "description": "An array of channels from which this tier can be accessed."}, {"name": "currencies", "data_type": "array", "is_categorical": false, "description": "An array of currencies applicable to this tier."}, {"name": "code", "data_type": "string", "is_categorical": true, "description": "A unique code associated with the tier for reference.", "found_categorical_values": ["None", "REGULATED_DEBIT", "NEW_USER", "REGULAR_USER", "UNREGULATED_DEBIT", "CHURNED_USER", "INACTIVE_USER", "ACH_BANK"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"name\": \"Randomized Offer Promo\",\n  \"discount\": 0.05,\n  \"isActive\": true,\n  \"transactionCountThreshold\": 15,\n  \"type\": \"promotion\",\n  \"createdAt\": \"2023-10-01T12:00:00.000000\",\n  \"updatedAt\": \"2024-09-01T15:00:00.000000\",\n  \"__v\": 1,\n  \"code\": \"RANDOMIZED_OFFER\",\n  \"admin\": {\n    \"id\": \"abcdef**********abcdef12\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"marketing.manager\"\n  },\n  \"allowedDestChannels\": [],\n  \"allowedSourceChannels\": [],\n  \"currencies\": []\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "escrows", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "currencies", "description": "This collection stores information about different currencies used in various transactions. It provides details such as the currency name, code, symbol, and its active status. This table contains the following fields: _id, name, code, symbol, isActive, __v, params.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each currency document, automatically generated by MongoDB."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The full name of the currency (e.g., United States Dollar)."}, {"name": "code", "data_type": "string", "is_categorical": false, "description": "The three-letter ISO 4217 code representing the currency (e.g., USD for United States Dollar)."}, {"name": "symbol", "data_type": "string", "is_categorical": true, "description": "The symbol used to represent the currency (e.g., $ for United States Dollar).", "found_categorical_values": ["$", "GH₵", "CA$", "£", "₦", "Rs", "ETB", "EGP", "₱", "CN¥", "MX$", "HTG", "Ksh", "GNF", "UGX", "€", "CFA", "FCFA", "R$", "₹"]}, {"name": "isActive", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the currency is currently active and in use.", "found_categorical_values": ["True"]}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to manage document versioning.", "found_categorical_values": ["0"]}, {"name": "params", "data_type": "object", "is_categorical": false, "description": "An object that may contain additional parameters or metadata related to the currency.", "nested_fields": [{"name": "params.withdrawalFeeBasisPoints", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "100"]}]}, {"name": "symbol", "data_type": "string", "is_categorical": true, "description": "The symbol used to represent the currency (e.g., $ for United States Dollar).", "found_categorical_values": ["$", "FCFA", "GNF", "Ksh", "HTG", "GH₵", "₹", "R$", "Rs", "UGX", "€", "EGP", "CFA", "ETB", "CA$", "₦", "£", "MX$", "CN¥", "₱"]}, {"name": "params", "data_type": "object", "is_categorical": false, "description": "An object that may contain additional parameters or metadata related to the currency.", "nested_fields": [{"name": "params.referralPayoutRate", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None", "1", "60", "600", "20", "1000", "30", "100"]}]}], "sample_documents": "{\n  \"_id\": \"72c90b4b374967g302e02483\",\n  \"name\": \"Euro\",\n  \"code\": \"EUR\",\n  \"symbol\": \"€\",\n  \"isActive\": false,\n  \"__v\": 1,\n  \"params\": {\n    \"withdrawalFeeBasisPoints\": \"150\"\n  }\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "referralcodes", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "assets", "description": "This collection stores information about various digital assets held by users, including their ownership details and associated metadata. This table contains the following fields: _id, symbol, amount, owner, createdAt, updatedAt, __v, name, publicKey, privateSecret, baseAddress, wallet, privateKey, eventId. The purpose of this collection is to facilitate the management and tracking of digital assets, ensuring that users can easily access and manage their holdings.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each asset record, automatically generated by MongoDB."}, {"name": "symbol", "data_type": "string", "is_categorical": true, "description": "The trading symbol or ticker for the asset, used for identification in markets.", "found_categorical_values": ["BTC", "USD", "NGN", "KES", "GBP", "UGX", "GHS", "CAD", "EUR"]}, {"name": "amount", "data_type": "string", "is_categorical": false, "description": "The quantity of the asset owned by the user."}, {"name": "owner", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who owns the asset."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the asset record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the asset record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to track document revisions.", "found_categorical_values": ["0", "None"]}, {"name": "symbol", "data_type": "string", "is_categorical": true, "description": "The trading symbol or ticker for the asset, used for identification in markets.", "found_categorical_values": ["BTC", "USD", "NGN", "KES", "GHS", "UGX", "GBP", "EUR", "USDT"]}, {"name": "name", "data_type": "string", "is_categorical": true, "description": "The full name of the asset, providing a more descriptive identification.", "found_categorical_values": ["None", "Bitcoin"]}, {"name": "public<PERSON>ey", "data_type": "string", "is_categorical": false, "description": "The public key associated with the asset, used for transactions."}, {"name": "privateSecret", "data_type": "string", "is_categorical": false, "description": "A secret key used for secure transactions, kept confidential."}, {"name": "baseAddress", "data_type": "object", "is_categorical": false, "description": "The base address for the asset, often used in wallet configurations.", "nested_fields": [{"name": "baseAddress.path", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "m/0"]}, {"name": "baseAddress.public", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "baseAddress.address", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "wallet", "data_type": "array", "is_categorical": false, "description": "The wallet address where the asset is stored.", "array_items": [{"name": "wallet[].chain_addresses", "data_type": "array", "is_categorical": false, "description": "", "array_items": [{"name": "wallet[].chain_addresses[].path", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "wallet[].chain_addresses[].public", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "wallet[].chain_addresses[].address", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "privateKey", "data_type": "string", "is_categorical": false, "description": "The private key for accessing the wallet, kept secure and confidential."}, {"name": "eventId", "data_type": "string", "is_categorical": false, "description": "An identifier for events related to the asset, such as transactions or updates."}], "sample_documents": "{\n  \"_id\": \"72f8a3b45cd64113fa8069ef\",\n  \"symbol\": \"USD\",\n  \"amount\": \"12.34\",\n  \"owner\": \"72b1f4a29a61a1337586898b\",\n  \"createdAt\": \"2023-02-15T10:30:45.123000\",\n  \"updatedAt\": \"2023-02-15T10:45:12.456000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "referrals", "description": "This collection stores information about referral transactions between users in the system. It tracks who referred whom, the amounts involved, and the status of the referral process.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each referral record."}, {"name": "fromUserName", "data_type": "string", "is_categorical": false, "description": "The username of the user who made the referral."}, {"name": "toUserName", "data_type": "string", "is_categorical": false, "description": "The username of the user who is being referred."}, {"name": "to<PERSON>ame", "data_type": "string", "is_categorical": false, "description": "The full name of the user being referred."}, {"name": "toPhone", "data_type": "string", "is_categorical": false, "description": "The phone number of the user being referred."}, {"name": "toUserId", "data_type": "string", "is_categorical": false, "description": "The unique identifier of the user being referred."}, {"name": "fromUserId", "data_type": "string", "is_categorical": false, "description": "The unique identifier of the user who made the referral."}, {"name": "cumulativeTransactionAmount", "data_type": "integer", "is_categorical": false, "description": "The total amount of transactions made by the referred user."}, {"name": "isFromStaff", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating if the referral was made by a staff member.", "found_categorical_values": ["False", "True"]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the referral (e.g., pending, completed).", "found_categorical_values": ["joined", "resolved", "transacted", "qualified"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the referral was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the referral record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB for document versioning.", "found_categorical_values": ["0"]}, {"name": "fromAmount", "data_type": "integer", "is_categorical": true, "description": "The amount associated with the referral from the referring user.", "found_categorical_values": ["5", "None"]}, {"name": "toAmount", "data_type": "integer", "is_categorical": true, "description": "The amount associated with the referral for the referred user.", "found_categorical_values": ["5", "None", "20"]}, {"name": "isToFulfilled", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating if the referral for the referred user has been fulfilled.", "found_categorical_values": ["None", "True"]}, {"name": "yearFulfilled", "data_type": "integer", "is_categorical": true, "description": "The year in which the referral was fulfilled.", "found_categorical_values": ["None", "2023", "2024"]}, {"name": "isFromFulfilled", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating if the referral from the referring user has been fulfilled.", "found_categorical_values": ["None", "True"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"fromUserName\": \"user12345\",\n  \"toUserName\": \"user67890\",\n  \"toName\": \"Sample Company\",\n  \"toPhone\": \"+*************\",\n  \"toUserId\": \"a1b2c3d4e5f67890abcdef13\",\n  \"fromUserId\": \"a1b2c3d4e5f67890abcdef14\",\n  \"cumulativeTransactionAmount\": 0,\n  \"isFromStaff\": false,\n  \"status\": \"joined\",\n  \"createdAt\": \"2023-11-01T10:00:00.000000\",\n  \"updatedAt\": \"2023-11-01T10:00:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "solidwebhooksecrets", "description": "This collection stores the secrets and configurations for webhooks used in the application. It ensures secure communication between the application and external services by managing webhook URLs and their associated secrets. This table contains the following fields: _id, webhook, url, secret, webhookId, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection, automatically generated by MongoDB."}, {"name": "webhook", "data_type": "array", "is_categorical": false, "description": "The name or identifier of the webhook associated with the secret."}, {"name": "url", "data_type": "string", "is_categorical": false, "description": "The endpoint URL where the webhook will send data."}, {"name": "secret", "data_type": "string", "is_categorical": false, "description": "A secret token used to verify the authenticity of the webhook requests."}, {"name": "webhookId", "data_type": "string", "is_categorical": false, "description": "An identifier that links the webhook to its configuration or usage context."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to manage document versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"7f3a1c2d5bcae1234fa56789\",\n  \"webhook\": [\n    \"person.created\",\n    \"person.kyc.approved\",\n    \"person.kyc.declined\",\n    \"person.kyc.review\",\n    \"person.kyc.updated\",\n    \"person.updated\"\n  ],\n  \"url\": \"https://3f4e-123-456-789-101.ngrok.io/solid-webhooks\",\n  \"secret\": \"sk_test_12ab34cd5678ef90gh123456ijklmnop\",\n  \"webhookId\": \"wbh-1a2b3c4d-5e6f-7890-abcd-ef**********\",\n  \"createdAt\": \"2023-01-15T10:30:45.123000\",\n  \"updatedAt\": \"2023-01-15T10:30:45.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "users-devices", "description": "This collection stores information about the devices associated with users in the application. It tracks the relationship between users and their devices, allowing for device management and user-device interactions.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each document in the collection, automatically generated by MongoDB."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The unique identifier of the user associated with the device, linking the device to the user's profile."}, {"name": "deviceId", "data_type": "string", "is_categorical": false, "description": "The unique identifier of the device, which can be used to track and manage the specific device."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was created, used for tracking the creation date of the user-device relationship."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating the last time the document was updated, used for tracking changes to the user-device relationship."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by Mongoose to manage document versioning and ensure data consistency.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"7f3a1e2b4c5d6e7f8g9h0i1j2\",\n  \"userId\": \"7f3a1e2b4c5d6e7f8g9h0i1k3\",\n  \"deviceId\": \"**********abcdef\",\n  \"createdAt\": \"2023-02-15T14:30:45.123000\",\n  \"updatedAt\": \"2023-02-15T14:30:45.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "referral-invites", "description": "This collection stores information about referral invites sent by users to their contacts. It tracks the details of each invite, including the referrer and the status of the invite.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each referral invite document."}, {"name": "contactHash", "data_type": "string", "is_categorical": false, "description": "A hashed representation of the contact's information to ensure privacy."}, {"name": "referrerId", "data_type": "string", "is_categorical": true, "description": "The identifier of the user who sent the referral invite.", "found_categorical_values": ["645e3d021482ce0f22e04e5c", "6430443eced0e906755eb2d5", "6523c7017d6c758a8b8f4bd4", "64a482fe20518e91d73d0426", "64a466a220518e91d73ca6cb"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the referral invite was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating the last time the referral invite was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to manage document versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"b3f1e2c4a7d8f9e0b1c2d3e4\",\n  \"contactHash\": \"a1b2c3d4e5f67890abcdef**********abcdef**********abcdef**********\",\n  \"referrerId\": \"d4e5f6a7b8c9d0e1f2g3h4i5\",\n  \"createdAt\": \"2024-05-05T12:34:56.789000\",\n  \"updatedAt\": \"2024-05-05T12:34:56.789000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "binanceorders", "description": "This collection stores information about orders placed on the Binance trading platform. It tracks various details related to each order, including user information, transaction status, and financial details.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each order in the collection."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier for the user who placed the order."}, {"name": "merchantTradeNo", "data_type": "string", "is_categorical": false, "description": "A unique trade number assigned by the merchant for tracking purposes."}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the order (e.g., pending, completed, canceled).", "found_categorical_values": ["PAY_CREATED", "PAY_SUCCESS_AMOUNT_DIFFERENT", "PAY_SUCCESS", "PAY_CLOSED"]}, {"name": "paymentId", "data_type": "string", "is_categorical": false, "description": "The identifier for the payment associated with the order."}, {"name": "amountUSD", "data_type": "float", "is_categorical": false, "description": "The total amount of the order in US dollars."}, {"name": "amountCurrency", "data_type": "integer", "is_categorical": false, "description": "The amount of currency involved in the order."}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The type of currency used in the order (e.g., BTC, ETH).", "found_categorical_values": ["NGN", "USD"]}, {"name": "commission", "data_type": "integer", "is_categorical": true, "description": "The commission fee charged for the order.", "found_categorical_values": ["0", "3"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the order was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the order was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB for document versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"userId\": \"a1b2c3d4e5f67890abcdef34\",\n  \"merchantTradeNo\": \"12ab34cd56ef78gh90ij12klmnopqrstu\",\n  \"status\": \"PAY_COMPLETED\",\n  \"paymentId\": \"********1012345678\",\n  \"amountUSD\": 0.05678912345,\n  \"amountCurrency\": 50,\n  \"currency\": \"USD\",\n  \"commission\": 1,\n  \"createdAt\": \"2023-10-01T12:00:00.000000\",\n  \"updatedAt\": \"2023-10-01T12:00:00.000000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "entityverifications", "description": "This collection stores verification records for entities associated with users, tracking the status and details of the verification process.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each verification record."}, {"name": "user_id", "data_type": "string", "is_categorical": false, "description": "The identifier of the user associated with the entity being verified."}, {"name": "entity_id", "data_type": "string", "is_categorical": false, "description": "The identifier of the entity that is undergoing verification."}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the verification process (e.g., pending, approved, rejected).", "found_categorical_values": ["pending"]}, {"name": "kyc_vendor", "data_type": "string", "is_categorical": true, "description": "The vendor responsible for conducting the Know Your Customer (KYC) verification.", "found_categorical_values": ["solid"]}, {"name": "vendor_id", "data_type": "string", "is_categorical": false, "description": "The identifier assigned by the KYC vendor for tracking the verification."}, {"name": "submitted_at", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the verification request was submitted."}, {"name": "responses", "data_type": "array", "is_categorical": false, "description": "A collection of responses received from the KYC vendor regarding the verification."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the verification record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the verification record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for managing document revisions.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"f3a1b2c4d5e6f7g8h9i0j1k2l\",\n  \"user_id\": \"a1b2c3d4e5f6g7h8i9j0k1l2m\",\n  \"entity_id\": \"b1c2d3e4f5g6h7i8j9k0l1m2n\",\n  \"status\": \"approved\",\n  \"kyc_vendor\": \"example\",\n  \"vendor_id\": \"kyc-1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p\",\n  \"submitted_at\": \"2023-06-15T12:34:56\",\n  \"responses\": [],\n  \"createdAt\": \"2023-06-15T12:34:57.802000\",\n  \"updatedAt\": \"2023-06-15T12:34:57.802000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "bulkuploadpayments", "description": "This collection stores information related to bulk upload payment transactions, tracking the status and details of each upload process.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each document in the collection."}, {"name": "url", "data_type": "string", "is_categorical": false, "description": "The URL where the bulk upload file is located."}, {"name": "successCount", "data_type": "integer", "is_categorical": true, "description": "The number of successful payment transactions processed.", "found_categorical_values": ["1", "2", "0", "5", "4", "22", "3"]}, {"name": "processingCount", "data_type": "integer", "is_categorical": true, "description": "The number of payment transactions currently being processed.", "found_categorical_values": ["1", "2", "5", "4", "3", "22"]}, {"name": "failedCount", "data_type": "integer", "is_categorical": true, "description": "The number of payment transactions that failed during processing.", "found_categorical_values": ["0", "1"]}, {"name": "user", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who initiated the bulk upload."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the bulk upload record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the bulk upload record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document versions.", "found_categorical_values": ["0"]}, {"name": "processingCount", "data_type": "integer", "is_categorical": true, "description": "The number of payment transactions currently being processed.", "found_categorical_values": ["1", "2", "5", "4", "22", "3"]}, {"name": "comment", "data_type": "string", "is_categorical": false, "description": "Any additional comments or notes related to the bulk upload."}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of payment or transaction being processed in the bulk upload.", "found_categorical_values": ["None", "debit", "credit", "cscdscdd"]}], "sample_documents": "{\n  \"_id\": \"73c9e1f4b2d3a4e8b1f8e9a1\",\n  \"url\": \"https://example-bulk-upload-dev.s3.amazonaws.com/123e4567e89b12d3a456426655440000-2023-10-01-15-30-45.csv\",\n  \"successCount\": 5,\n  \"processingCount\": 3,\n  \"failedCount\": 1,\n  \"user\": \"123e4567e89b12d3a456426655440001\",\n  \"createdAt\": \"2023-10-01T14:30:56.911000\",\n  \"updatedAt\": \"2023-10-01T14:30:56.911000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "highfrequencyreceivers", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "selltransactions", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "transferfiats", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "cellulant_transactions", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "rewards", "description": "This collection stores information about various rewards that can be redeemed by users. This table contains the following fields: _id, type, pointsValue, dollarValue, description, name, imageLink, supportedCountries, provider, isDeactivated, tier, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each reward document, automatically generated by MongoDB."}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The category or type of the reward, such as gift card, merchandise, or experience.", "found_categorical_values": ["gift_card", "coupon"]}, {"name": "pointsValue", "data_type": "integer", "is_categorical": false, "description": "The number of points required to redeem the reward."}, {"name": "dollarValue", "data_type": "integer", "is_categorical": true, "description": "The monetary value of the reward, representing its worth in dollars.", "found_categorical_values": ["10", "20", "0", "50", "2", "100"]}, {"name": "description", "data_type": "string", "is_categorical": false, "description": "A detailed description of the reward, including any relevant information."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The name of the reward, which is displayed to users."}, {"name": "imageLink", "data_type": "string", "is_categorical": false, "description": "A URL linking to an image of the reward, used for display purposes."}, {"name": "supportedCountries", "data_type": "array", "is_categorical": false, "description": "A list of countries where the reward can be redeemed."}, {"name": "provider", "data_type": "string", "is_categorical": true, "description": "The entity or company that provides the reward.", "found_categorical_values": ["uber", "None", "southwest", "amazon"]}, {"name": "isDeactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating whether the reward is currently active or has been deactivated.", "found_categorical_values": ["False"]}, {"name": "tier", "data_type": "string", "is_categorical": true, "description": "The tier level associated with the reward, which may affect eligibility or availability.", "found_categorical_values": ["member", "platinum"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the reward document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the reward document was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "dollarValue", "data_type": "integer", "is_categorical": true, "description": "The monetary value of the reward, representing its worth in dollars.", "found_categorical_values": ["10", "20", "0", "2", "50", "100"]}], "sample_documents": "{\n  \"_id\": \"7f3c1a2b4d5e6f7g8h9i0j1k2\",\n  \"type\": \"gift_card\",\n  \"pointsValue\": 1500,\n  \"dollarValue\": 15,\n  \"description\": \"Example\",\n  \"name\": \"Amazon Gift Card\",\n  \"imageLink\": \"reward_card_image_abc12345-6789-0def-ghij-klmnopqrstuv\",\n  \"supportedCountries\": [\n    \"CA\"\n  ],\n  \"provider\": \"amazon\",\n  \"isDeactivated\": true,\n  \"tier\": \"premium\",\n  \"createdAt\": \"2024-03-01T14:30:45.123000\",\n  \"updatedAt\": \"2024-12-01T11:15:30.456000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "rates-series", "description": "This collection stores currency exchange rates between different currencies over time. It is used to track historical exchange rates and provide insights into currency fluctuations.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "fromCurrency", "data_type": "string", "is_categorical": true, "description": "The currency code of the currency being exchanged from.", "found_categorical_values": ["UGX", "KES", "EUR", "NGN", "GHS", "CAD", "GBP", "USD"]}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "data_type": "string", "is_categorical": true, "description": "The currency code of the currency being exchanged to.", "found_categorical_values": ["CAD", "NGN", "UGX", "KES", "EUR", "GHS", "USD", "GBP"]}, {"name": "rate", "data_type": "string", "is_categorical": false, "description": "The exchange rate from the fromCurrency to the toCurrency."}, {"name": "timestamp", "data_type": "object", "is_categorical": false, "description": "The date and time when the exchange rate was recorded.", "nested_fields": [{"name": "timestamp.h", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["16", "18", "14"]}, {"name": "timestamp.d", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["9", "6"]}, {"name": "timestamp.m", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["9", "7"]}, {"name": "timestamp.y", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["2022", "2023"]}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the document was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to manage document versioning.", "found_categorical_values": ["0"]}, {"name": "fromCurrency", "data_type": "string", "is_categorical": true, "description": "The currency code of the currency being exchanged from.", "found_categorical_values": ["EUR", "UGX", "USD", "GHS", "GBP", "CAD", "NGN", "KES"]}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "data_type": "string", "is_categorical": true, "description": "The currency code of the currency being exchanged to.", "found_categorical_values": ["EUR", "KES", "USD", "UGX", "GBP", "CAD", "NGN", "GHS"]}, {"name": "timestamp", "data_type": "object", "is_categorical": false, "description": "The date and time when the exchange rate was recorded.", "nested_fields": [{"name": "timestamp.h", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["18", "16", "14"]}, {"name": "timestamp.d", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["9", "6"]}, {"name": "timestamp.m", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["9", "7"]}, {"name": "timestamp.y", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["2022", "2023"]}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f6789012345678\",\n  \"fromCurrency\": \"USD\",\n  \"toCurrency\": \"EUR\",\n  \"rate\": \"0.85\",\n  \"timestamp\": {\n    \"h\": 14,\n    \"d\": 15,\n    \"m\": 30,\n    \"y\": 2023\n  },\n  \"createdAt\": \"2023-03-15T14:45:12.123000\",\n  \"updatedAt\": \"2023-03-15T14:45:12.123000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "solidcards", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "payments", "description": "This collection stores information related to payment transactions made by users. It tracks the details of each payment, including the amount, currency, payment method, and status of the transaction. This table contains the following fields: _id, idempotency_key, amount, currency, payment_method_id, payment_method_type, user_id, status, internal_data, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each payment document, automatically generated by MongoDB."}, {"name": "idempotency_key", "data_type": "string", "is_categorical": false, "description": "A unique key used to prevent duplicate payment processing."}, {"name": "amount", "data_type": "integer", "is_categorical": false, "description": "The total amount of the payment, typically represented in the smallest currency unit (e.g., cents for USD)."}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the payment is made, represented by its ISO 4217 code (e.g., 'USD', 'EUR').", "found_categorical_values": ["USD", "GBP", "CAD"]}, {"name": "payment_method_id", "data_type": "string", "is_categorical": true, "description": "The identifier for the payment method used for the transaction.", "found_categorical_values": ["642ffff7675f8d607d09ef20", "64304fbaced0e906755f0c2d", "62c3e6c9eeda9be3ec1ae2d2", "64304af3ced0e906755ef363", "643015fa675f8d607d0a658c", "637e10621529462c523a896f", "61165c60b1a1f65f0110c2ee", "6318c1b82e7d019721c8c3f4", "6266a7e083e00b7e3af83afa", "62e7e26ebee74f79e24988d7", "62e75b27a9f3476675ecaa61", "63c188ec2260e0c72cfea95e", "63ce830d112c142e3e0601b2", "630e1f4e70e711aeb0343e14", "6447ba7814ff88c68bb776dd", "6239b5f1a99523bb3dd8cd19", "638834b8d3cbc6cb3fc1146a", "6441ca1c3ed247989fe62c3b", "63e3b3db08c7cf37266139c4", "6220dea313280b2aad80b2ad", "62ef65212e9ddca00727ee98", "644113b81188c1b0696ee0c2", "644be4b164a7b83a6fb00cc1", "64122dc90d691ac33e5f1732", "63316e91519315efd1d73047", "63209dc2e2f0cbfd477e60bc", "642edbe367c1bed3cd43beb5", "63e60a8583c515e7e2cb25cc", "61183fb38ac2dd203908a743", "63c6f21aedc6490d9f5f23a5", "63063c6f21313b841ff29fc8", "640cc3eeadd60053aa5138f4", "62b1c508eb5014e7dfe379ca", "63dd4db5e58bc7e6ed9c7bac", "63dd08f63e1fa61d158498f9", "64565d59c6c4d6b727e13d9c", "64117232a7e96aebc555c341", "622a4878d2a998ca9f7fe0d9", "61b2331df9ba1747d4882914", "63184074456f3e62d964ac4d", "642fe01e675f8d607d0990d8", "642c2f07df65f5716baf1542", "63c8118e79a03c1238507dd2", "611c249586a6ca5c865071c1", "63c820c4044a0f614dc6668d", "616d7295ef867e848a7f149c", "62a89867b3b43cf43254ba6d", "63dd66c4e58bc7e6ed9d1ba6", "637a4da585740fe1d22950ac", "642fe06d675f8d607d099299"]}, {"name": "payment_method_type", "data_type": "string", "is_categorical": true, "description": "The type of payment method (e.g., credit card, PayPal) used for the transaction.", "found_categorical_values": ["card", "bank_account", "card-payout"]}, {"name": "user_id", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who made the payment."}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the payment (e.g., pending, completed, failed).", "found_categorical_values": ["succeeded", "pending"]}, {"name": "internal_data", "data_type": "object", "is_categorical": false, "description": "Any additional internal data related to the payment, stored as a JSON object.", "nested_fields": [{"name": "internal_data.provider", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["stripe", "sila"]}, {"name": "internal_data.transaction_id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internal_data.provider_status", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["succeeded", "success", "None", "pending"]}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the payment record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the payment record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "payment_method_id", "data_type": "string", "is_categorical": true, "description": "The identifier for the payment method used for the transaction.", "found_categorical_values": ["642ffff7675f8d607d09ef20", "64304fbaced0e906755f0c2d", "62c3e6c9eeda9be3ec1ae2d2", "643015fa675f8d607d0a658c", "64304af3ced0e906755ef363", "637e10621529462c523a896f", "6318c1b82e7d019721c8c3f4", "61165c60b1a1f65f0110c2ee", "62e7e26ebee74f79e24988d7", "6266a7e083e00b7e3af83afa", "62e75b27a9f3476675ecaa61", "63ce830d112c142e3e0601b2", "63c188ec2260e0c72cfea95e", "6441ca1c3ed247989fe62c3b", "6239b5f1a99523bb3dd8cd19", "638834b8d3cbc6cb3fc1146a", "6447ba7814ff88c68bb776dd", "644113b81188c1b0696ee0c2", "630e1f4e70e711aeb0343e14", "644be4b164a7b83a6fb00cc1", "63316e91519315efd1d73047", "63209dc2e2f0cbfd477e60bc", "64122dc90d691ac33e5f1732", "6220dea313280b2aad80b2ad", "63e3b3db08c7cf37266139c4", "62ef65212e9ddca00727ee98", "63e60a8583c515e7e2cb25cc", "61183fb38ac2dd203908a743", "640cc3eeadd60053aa5138f4", "642edbe367c1bed3cd43beb5", "63dd4db5e58bc7e6ed9c7bac", "616d7295ef867e848a7f149c", "622de175ed03fb8d6245a979", "63c820c4044a0f614dc6668d", "64117232a7e96aebc555c341", "6458fc35c41065f0663ebc89", "63e4f73a1dc515ff83bf7eb2", "61b2331df9ba1747d4882914", "63dd08f63e1fa61d158498f9", "63063c6f21313b841ff29fc8", "6403bf4e9f52469b2e522a64", "62a89867b3b43cf43254ba6d", "622a4878d2a998ca9f7fe0d9", "611c249586a6ca5c865071c1", "63c17c082541bc625dad3713", "611d1ec2c87f2f0821782b87", "61b22bb38dbe7f41ea48db0d", "645a595e3406091d0ab07fa4", "64565d59c6c4d6b727e13d9c", "62b1c508eb5014e7dfe379ca"]}, {"name": "internal_data", "data_type": "object", "is_categorical": false, "description": "Any additional internal data related to the payment, stored as a JSON object.", "nested_fields": [{"name": "internal_data.provider", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["stripe"]}, {"name": "internal_data.transaction_id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internal_data.provider_status", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["succeeded", "success", "None", "pending"]}]}], "sample_documents": "{\n  \"_id\": \"7f3b0cda8e4f2a3b9c4e1f2a\",\n  \"idempotency_key\": \"a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6\",\n  \"amount\": 250,\n  \"currency\": \"EUR\",\n  \"payment_method_id\": \"8a9b0c1d2e3f4g5h6i7j8k9l0\",\n  \"payment_method_type\": \"paypal\",\n  \"user_id\": \"9a8b7c6d5e4f3g2h1i0j9k8l\",\n  \"status\": \"pending\",\n  \"internal_data\": {\n    \"provider\": \"paypal\",\n    \"transaction_id\": \"txn_1A2B3C4D5E6F7G8H9I0J\",\n    \"provider_status\": \"pending\"\n  },\n  \"createdAt\": \"2022-01-15T12:00:00.000000\",\n  \"updatedAt\": \"2022-01-15T12:00:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "rewards-epoches", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "tokens", "description": "This collection stores authentication tokens associated with users in the system. It is used to manage user sessions and ensure secure access to resources. This table contains the following fields: _id, userId, token, expiresAt, children, createdAt, updatedAt.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each token document, automatically generated by MongoDB."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user associated with the token, linking the token to a specific user."}, {"name": "token", "data_type": "string", "is_categorical": false, "description": "The actual token string used for authentication, which is typically a secure, random value."}, {"name": "expiresAt", "data_type": "integer", "is_categorical": false, "description": "A timestamp indicating when the token will expire and no longer be valid."}, {"name": "children", "data_type": "array", "is_categorical": false, "description": "An array of child tokens, if any, representing tokens that are derived from this token."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the token document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating the last time the token document was updated."}, {"name": "expiresAt", "data_type": "float", "is_categorical": false, "description": "A timestamp indicating when the token will expire and no longer be valid."}], "sample_documents": "{\n  \"_id\": \"5f4e3d2c1b0a4e3f8c0b1a2b\",\n  \"userId\": \"5f4e3d2c1b0a4e3f8c0b1a3c\",\n  \"token\": \"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", "status": "deactivated"}, {"database_name": "dev", "collection_name": "medici_locks", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "payouts", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "rates-otcs", "description": "This collection stores the exchange rates for over-the-counter (OTC) transactions between various currency pairs. This table contains the following fields: _id, fromSymbol, toSymbol, value, inverse, source, batchId, createdAt, updatedAt.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol from which the exchange rate is calculated.", "found_categorical_values": ["USD", "INR", "NGN", "EUR", "GBP", "KES", "XOF", "UGX", "HTG", "CAD", "GNF", "EGP", "GHS", "CNY", "ETB", "XAF", "PHP", "MXN", "PKR", "MGA", "ZMW", "ZAR", "TZS", "MZN", "MWK"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol to which the exchange rate is applied.", "found_categorical_values": ["USD", "NGN", "KES", "XAF", "HTG", "GHS", "GBP", "CAD", "EGP", "CNY", "MXN", "INR", "EUR", "GNF", "PHP", "ETB", "XOF", "UGX", "PKR"]}, {"name": "value", "data_type": "string", "is_categorical": false, "description": "The exchange rate value between the fromSymbol and toSymbol."}, {"name": "inverse", "data_type": "string", "is_categorical": false, "description": "The inverse exchange rate value, calculated as 1 divided by the value."}, {"name": "source", "data_type": "string", "is_categorical": true, "description": "The source from which the exchange rate data was obtained.", "found_categorical_values": ["binance", "terrapay", "None", "google", "afriex", "stripe", "zeepay", "dlocal", "ejara", "cellulant", "Terrapay", "system", "Binance"]}, {"name": "batchId", "data_type": "string", "is_categorical": false, "description": "An identifier for the batch of rates, useful for tracking updates."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was last updated."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol from which the exchange rate is calculated.", "found_categorical_values": ["USD", "EGP", "EUR", "GBP", "GNF", "XOF", "HTG", "KES", "CNY", "UGX", "CAD", "MXN", "INR", "ETB", "GHS", "PHP", "XAF", "NGN", "PKR", "RWF", "ZMW", "MZN", "MWK", "ZAR", "MGA", "TZS"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol to which the exchange rate is applied.", "found_categorical_values": ["USD", "NGN", "KES", "CNY", "EGP", "GHS", "HTG", "GNF", "EUR", "ETB", "CAD", "XOF", "PHP", "INR", "MXN", "UGX", "GBP", "XAF", "PKR", "RWF"]}, {"name": "source", "data_type": "string", "is_categorical": true, "description": "The source from which the exchange rate data was obtained.", "found_categorical_values": ["binance", "terrapay", "None", "google", "afriex", "stripe", "zeepay", "dlocal", "cellulant", "ejara", "Terrapay", "Binance", "Stripe", "chapa"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f6789012345678\",\n  \"fromSymbol\": \"EUR\",\n  \"toSymbol\": \"NGN\",\n  \"value\": \"450.25\",\n  \"inverse\": \"0.002221123456\",\n  \"source\": \"examplepay\",\n  \"batchId\": \"a1b2c3d4e5f6789012345679\",\n  \"createdAt\": \"2023-10-27T11:12:34.123000\",\n  \"updatedAt\": \"2023-10-27T11:12:34.123000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "scheduled-transactions", "description": "This collection stores information about transactions that are scheduled to occur at specified intervals. It allows users to automate their financial transactions based on predefined schedules.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each scheduled transaction."}, {"name": "interval", "data_type": "string", "is_categorical": true, "description": "The frequency at which the transaction is scheduled to occur (e.g., daily, weekly, monthly).", "found_categorical_values": ["WEEKLY", "TWICE_A_MONTH", "MONTHLY"]}, {"name": "nextRunDate", "data_type": "string", "is_categorical": false, "description": "The date and time when the transaction is next scheduled to run."}, {"name": "recurrenceCount", "data_type": "integer", "is_categorical": true, "description": "The total number of times the transaction is set to recur.", "found_categorical_values": ["1", "5"]}, {"name": "transactionRequest", "data_type": "object", "is_categorical": false, "description": "The details of the transaction to be executed, including amount and recipient.", "nested_fields": [{"name": "transactionRequest.sourceAsset", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "USD", "EUR", "PKR", "GBP", "KES"]}, {"name": "transactionRequest.destinationAsset", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "KES", "ETB", "GHS", "USD", "EUR", "UGX", "GBP"]}, {"name": "transactionRequest.amount", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "transactionRequest.sourcePaymentMethodId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "transactionRequest.destinationPaymentMethodId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "transactionRequest.sourceChannel", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["WALLET", "CARD", "ACH_BANK_ACCOUNT"]}, {"name": "transactionRequest.destinationChannel", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["BANK_ACCOUNT", "MOBILE_MONEY"]}, {"name": "transactionRequest._id", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who created the scheduled transaction."}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the scheduled transaction (e.g., active, completed, canceled).", "found_categorical_values": ["CANCELLED", "ACTIVE", "PAUSED"]}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "transactionRequest", "data_type": "object", "is_categorical": false, "description": "The details of the transaction to be executed, including amount and recipient.", "nested_fields": [{"name": "transactionRequest.sourceAsset", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "USD", "EUR", "PKR", "GBP", "KES"]}, {"name": "transactionRequest.destinationAsset", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "KES", "ETB", "GHS", "USD", "EUR", "GBP", "UGX"]}, {"name": "transactionRequest.amount", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "transactionRequest.destinationPaymentMethodId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "transactionRequest.sourceChannel", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["WALLET", "CARD", "ACH_BANK_ACCOUNT"]}, {"name": "transactionRequest.destinationChannel", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["BANK_ACCOUNT", "MOBILE_MONEY"]}, {"name": "transactionRequest._id", "data_type": "string", "is_categorical": false, "description": ""}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f607g8h9i0j1k2l\",\n  \"interval\": \"WEEKLY\",\n  \"nextRunDate\": \"2024-01-10T14:30:00\",\n  \"recurrenceCount\": 3,\n  \"transactionRequest\": {\n    \"sourceAsset\": \"EUR\",\n    \"destinationAsset\": \"JPY\",\n    \"amount\": 25,\n    \"sourcePaymentMethodId\": \"a1b2c3d4e5f607g8h9i0j1k2m\",\n    \"destinationPaymentMethodId\": \"n1o2p3q4r5s607t8u9v0w1x2y\",\n    \"sourceChannel\": \"BANK_TRANSFER\",\n    \"destinationChannel\": \"CASH\",\n    \"_id\": \"z1a2b3c4d5e607f8g9h0i1j2k\"\n  },\n  \"userId\": \"m1n2o3p4q5r6s7t8u9v0w1x2y\",\n  \"status\": \"COMPLETED\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "blockedpaymentmethods", "description": "This collection stores information about payment methods that have been blocked for various reasons. It helps in managing and tracking blocked payment methods to ensure compliance and security in payment processing.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each blocked payment method record."}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of payment method that has been blocked (e.g., credit card, PayPal, etc.).", "found_categorical_values": ["CARD", "MOBILE_MONEY"]}, {"name": "accountIdentifier", "data_type": "string", "is_categorical": false, "description": "A unique identifier for the account associated with the blocked payment method."}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the payment method is registered or used.", "found_categorical_values": ["GH", "US"]}, {"name": "reason", "data_type": "string", "is_categorical": false, "description": "The reason for blocking the payment method (e.g., fraud, user request, etc.)."}, {"name": "provider", "data_type": "string", "is_categorical": true, "description": "The payment provider associated with the blocked payment method.", "found_categorical_values": ["None", "MTN"]}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who owns the blocked payment method."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the blocked payment method record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the blocked payment method record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of payment method that has been blocked (e.g., credit card, PayPal, etc.).", "found_categorical_values": ["MOBILE_MONEY", "CARD"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the payment method is registered or used.", "found_categorical_values": ["US", "GH"]}], "sample_documents": "{\n  \"_id\": \"72b8e1f93cde4a3b8a29c123\",\n  \"type\": \"MOBILE_MONEY\",\n  \"accountIdentifier\": \"************\",\n  \"country\": \"GH\",\n  \"reason\": \"Lost\",\n  \"provider\": \"AIRTEL\",\n  \"userId\": \"733c244cc772b1120bd60199\",\n  \"createdAt\": \"2024-02-20T09:15:30.123000\",\n  \"updatedAt\": \"2024-02-20T09:15:30.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "pendingtransactions", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "otc-volumes", "description": "The 'otc-volumes' collection stores data related to over-the-counter (OTC) trading volumes, capturing the details of currency exchanges and transactions. This table contains the following fields: _id, fromSymbol, toSymbol, rate, fromAmount, toAmount, country, submittedBy, timestamp, availableAmount, createdAt, updatedAt, __v, approvedBy.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The symbol of the currency being exchanged from.", "found_categorical_values": ["USD", "GBP", "XOF"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The symbol of the currency being exchanged to.", "found_categorical_values": ["NGN", "KES", "GHS"]}, {"name": "rate", "data_type": "string", "is_categorical": false, "description": "The exchange rate between the fromSymbol and toSymbol."}, {"name": "fromAmount", "data_type": "integer", "is_categorical": false, "description": "The amount of currency being exchanged from."}, {"name": "toAmount", "data_type": "integer", "is_categorical": false, "description": "The amount of currency being received in exchange."}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the transaction is taking place.", "found_categorical_values": ["None", "US", "NG", "KE", "DE"]}, {"name": "submittedBy", "data_type": "object", "is_categorical": false, "description": "The identifier of the user who submitted the transaction.", "nested_fields": [{"name": "submittedBy.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "submittedBy.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "submittedBy.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["engineering.manager", "finance.manager", "engineering"]}]}, {"name": "timestamp", "data_type": "string", "is_categorical": false, "description": "The date and time when the transaction was submitted."}, {"name": "availableAmount", "data_type": "float", "is_categorical": false, "description": "The amount of currency available for trading."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The date and time when the document was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used by Mongoose for versioning.", "found_categorical_values": ["0"]}, {"name": "approvedBy", "data_type": "object", "is_categorical": false, "description": "The identifier of the user who approved the transaction.", "nested_fields": [{"name": "approvedBy.id", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "622b244cc772b1120bd60141"]}, {"name": "approvedBy.name", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "approvedBy.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "engineering.manager", "finance.manager", "engineering"]}]}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The symbol of the currency being exchanged from.", "found_categorical_values": ["USD", "XOF", "GBP"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the transaction is taking place.", "found_categorical_values": ["None", "US", "NG", "DE", "KE"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"fromSymbol\": \"EUR\",\n  \"toSymbol\": \"UGX\",\n  \"rate\": \"120\",\n  \"fromAmount\": 500,\n  \"toAmount\": 60000,\n  \"country\": \"UG\",\n  \"submittedBy\": {\n    \"id\": \"f1e2d3c4b5a67890abcdef34\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"finance.director\"\n  },\n  \"timestamp\": \"2023-11-22T10:30:45.123000\",\n  \"availableAmount\": 50.25000000000045,\n  \"createdAt\": \"2023-11-22T10:30:46.789000\",\n  \"updatedAt\": \"2024-12-21T15:45:12.345000\",\n  \"__v\": 0,\n  \"approvedBy\": {\n    \"id\": \"f1e2d3c4b5a67890abcdef56\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"engineering.lead\"\n  }\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "afriexpaymentmethods", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "disputes", "description": "This collection is used to manage and track disputes related to transactions. It allows users to report issues with payments and provides a structured way to resolve these disputes.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each dispute record."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier for the user who initiated the dispute."}, {"name": "paymentId", "data_type": "string", "is_categorical": false, "description": "The identifier for the payment associated with the dispute."}, {"name": "internalData", "data_type": "object", "is_categorical": false, "description": "Additional internal information related to the dispute, used for processing.", "nested_fields": [{"name": "internalData.provider", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["AFRIEX_INTERNAL"]}, {"name": "internalData.providerId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internalData.providerReason", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internalData.providerStatus", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["created"]}]}, {"name": "amount", "data_type": "integer", "is_categorical": false, "description": "The amount of money involved in the dispute."}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the payment was made.", "found_categorical_values": ["USD", "ETB", "NGN"]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the dispute (e.g., open, resolved, closed).", "found_categorical_values": ["created", "fraud", "closed"]}, {"name": "reason", "data_type": "string", "is_categorical": false, "description": "The reason provided by the user for initiating the dispute."}, {"name": "notes", "data_type": "array", "is_categorical": false, "description": "Any additional notes or comments related to the dispute."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the dispute was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the dispute was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB for document versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"7f3b2a1c4e5f6a7b8c9d0e1f\",\n  \"userId\": \"5a6b7c8d9e0f1a2b3c4d5e6f\",\n  \"paymentId\": \"8f9e0d1c2b3a4e5f6g7h8i9j\",\n  \"internalData\": {\n    \"provider\": \"FAKE_PROVIDER\",\n    \"providerId\": \"5a6b7c8d9e0f1a2b3c4d5e6f\",\n    \"providerReason\": \"I want to challenge this\",\n    \"providerStatus\": \"pending\"\n  },\n  \"amount\": 256,\n  \"currency\": \"EUR\",\n  \"status\": \"pending\",\n  \"reason\": \"I want to challenge this\",\n  \"notes\": [],\n  \"createdAt\": \"2025-01-15T09:30:12.123000\",\n  \"updatedAt\": \"2025-01-15T09:30:12.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "plaidtransactions", "description": "This collection stores transaction data retrieved from financial institutions via the Plaid API, allowing users to track their financial activities and manage their accounts effectively.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each transaction record in the collection."}, {"name": "transaction_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier assigned to each transaction by Plaid."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier for the user associated with the transaction."}, {"name": "account_id", "data_type": "string", "is_categorical": false, "description": "The identifier for the account from which the transaction originated."}, {"name": "account_owner", "data_type": "null", "is_categorical": false, "description": "The name of the account owner."}, {"name": "amount", "data_type": "float", "is_categorical": false, "description": "The amount of money involved in the transaction."}, {"name": "authorized_date", "data_type": "null", "is_categorical": false, "description": "The date when the transaction was authorized."}, {"name": "authorized_datetime", "data_type": "null", "is_categorical": false, "description": "The exact date and time when the transaction was authorized."}, {"name": "category", "data_type": "array", "is_categorical": false, "description": "The category assigned to the transaction, indicating its type."}, {"name": "category_id", "data_type": "string", "is_categorical": true, "description": "A unique identifier for the transaction category.", "found_categorical_values": ["None", "********", "********", "********", "********", "********"]}, {"name": "check_number", "data_type": "null", "is_categorical": false, "description": "The check number associated with the transaction, if applicable."}, {"name": "counterparties", "data_type": "array", "is_categorical": false, "description": "Information about the other parties involved in the transaction."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the transaction record was created."}, {"name": "date", "data_type": "string", "is_categorical": false, "description": "The date when the transaction occurred."}, {"name": "datetime", "data_type": "null", "is_categorical": false, "description": "The exact date and time when the transaction occurred."}, {"name": "iso_currency_code", "data_type": "string", "is_categorical": true, "description": "The ISO code for the currency used in the transaction.", "found_categorical_values": ["USD"]}, {"name": "location", "data_type": "object", "is_categorical": false, "description": "The location where the transaction took place.", "nested_fields": [{"name": "location.address", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.city", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.region", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.postal_code", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.country", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.lat", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.lon", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "location.store_number", "data_type": "null", "is_categorical": false, "description": ""}]}, {"name": "logo_url", "data_type": "null", "is_categorical": false, "description": "A URL linking to the logo of the merchant associated with the transaction."}, {"name": "merchant_name", "data_type": "null", "is_categorical": false, "description": "The name of the merchant where the transaction occurred."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "A descriptive name for the transaction."}, {"name": "payment_channel", "data_type": "string", "is_categorical": true, "description": "The channel through which the payment was made (e.g., online, in-store).", "found_categorical_values": ["other", "in store", "online"]}, {"name": "payment_meta", "data_type": "object", "is_categorical": false, "description": "Additional metadata related to the payment.", "nested_fields": [{"name": "payment_meta.reference_number", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.ppd_id", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.payee", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.by_order_of", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.payer", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.payment_method", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.payment_processor", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "payment_meta.reason", "data_type": "null", "is_categorical": false, "description": ""}]}, {"name": "pending", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the transaction is pending.", "found_categorical_values": ["False"]}, {"name": "pending_transaction_id", "data_type": "null", "is_categorical": false, "description": "The identifier for the pending transaction, if applicable."}, {"name": "personal_finance_category", "data_type": "object", "is_categorical": false, "description": "A category for personal finance purposes.", "nested_fields": [{"name": "personal_finance_category.primary", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["OTHER", "FOOD_AND_DRINK", "TRAVEL", "TRANSPORTATION", "GENERAL_MERCHANDISE"]}, {"name": "personal_finance_category.detailed", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["OTHER", "FOOD_AND_DRINK_FAST_FOOD", "TRAVEL_FLIGHTS", "FOOD_AND_DRINK_COFFEE", "TRANSPORTATION_TAXIS_AND_RIDE_SHARES", "GENERAL_MERCHANDISE_OTHER_GENERAL_MERCHANDISE"]}]}, {"name": "personal_finance_category_icon_url", "data_type": "string", "is_categorical": true, "description": "A URL linking to an icon representing the personal finance category.", "found_categorical_values": ["https://plaid-category-icons.plaid.com/PFC_OTHER.png", "https://plaid-category-icons.plaid.com/PFC_FOOD_AND_DRINK.png", "https://plaid-category-icons.plaid.com/PFC_TRAVEL.png", "https://plaid-category-icons.plaid.com/PFC_TRANSPORTATION.png", "https://plaid-category-icons.plaid.com/PFC_GENERAL_MERCHANDISE.png"]}, {"name": "transaction_code", "data_type": "null", "is_categorical": false, "description": "A code representing the type of transaction."}, {"name": "transaction_type", "data_type": "string", "is_categorical": true, "description": "The type of transaction (e.g., debit, credit).", "found_categorical_values": ["unresolved", "place", "special"]}, {"name": "unofficial_currency_code", "data_type": "null", "is_categorical": false, "description": "An unofficial currency code, if applicable."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the transaction record was last updated."}, {"name": "website", "data_type": "null", "is_categorical": false, "description": "The website associated with the merchant, if available."}, {"name": "category_id", "data_type": "string", "is_categorical": true, "description": "A unique identifier for the transaction category.", "found_categorical_values": ["None", "********", "********", "********", "********", "********"]}, {"name": "personal_finance_category", "data_type": "object", "is_categorical": false, "description": "A category for personal finance purposes.", "nested_fields": [{"name": "personal_finance_category.primary", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["OTHER", "FOOD_AND_DRINK", "TRAVEL", "TRANSPORTATION", "GENERAL_MERCHANDISE"]}, {"name": "personal_finance_category.detailed", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["OTHER", "FOOD_AND_DRINK_FAST_FOOD", "FOOD_AND_DRINK_COFFEE", "TRAVEL_FLIGHTS", "TRANSPORTATION_TAXIS_AND_RIDE_SHARES", "GENERAL_MERCHANDISE_OTHER_GENERAL_MERCHANDISE"]}]}], "sample_documents": "{\n  \"_id\": \"12a3bc45d6789ef01234abcd\",\n  \"transaction_id\": \"ABcD1EfGhIjK2LmnOpQrStUvWxYz123456\",\n  \"userId\": \"789fgh01234ijklmno567pqrs\",\n  \"account_id\": \"1A2bC3dE4F5gH6I7jK8L9mN0oPqR\",\n  \"account_owner\": null,\n  \"amount\": 12.99,\n  \"authorized_date\": null,\n  \"authorized_datetime\": null,\n  \"category\": [\n    \"Entertainment\",\n    \"Movies\",\n    \"Cinema\"\n  ],\n  \"category_id\": \"********\",\n  \"check_number\": null,\n  \"counterparties\": [],\n  \"createdAt\": \"2024-02-15T14:30:45.123000\",\n  \"date\": \"2023-06-15\",\n  \"datetime\": null,\n  \"iso_currency_code\": \"EUR\",\n  \"location\": {\n    \"address\": null,\n    \"city\": null,\n    \"region\": null,\n    \"postal_code\": null,\n    \"country\": null,\n    \"lat\": null,\n    \"lon\": null,\n    \"store_number\": null\n  },\n  \"logo_url\": null,\n  \"merchant_name\": null,\n  \"name\": \"Cineplex\",\n  \"payment_channel\": \"online\",\n  \"payment_meta\": {\n    \"reference_number\": null,\n    \"ppd_id\": null,\n    \"payee\": null,\n    \"by_order_of\": null,\n    \"payer\": null,\n    \"payment_method\": null,\n    \"payment_processor\": null,\n    \"reason\": null\n  },\n  \"pending\": true,\n  \"pending_transaction_id\": null,\n  \"personal_finance_category\": {\n    \"primary\": \"ENTERTAINMENT\",\n    \"detailed\": \"ENTERTAINMENT_MOVIES\"\n  },\n  \"personal_finance_category_icon_url\": \"https://plaid-category-icons.plaid.com/PFC_ENTERTAINMENT.png\",\n  \"transaction_code\": null,\n  \"transaction_type\": \"purchase\",\n  \"unofficial_currency_code\": null,\n  \"updatedAt\": \"2024-02-15T14:30:45.123000\",\n  \"website\": null\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "medici_balances", "description": "This collection stores financial balance records for various accounts, tracking transactions and balances over time.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "key", "data_type": "string", "is_categorical": false, "description": "A unique key representing the specific account or entity associated with the balance."}, {"name": "<PERSON><PERSON><PERSON>", "data_type": "string", "is_categorical": false, "description": "The original key used for external references or integrations."}, {"name": "book", "data_type": "string", "is_categorical": true, "description": "The name of the financial book or ledger to which this balance belongs.", "found_categorical_values": ["AfriexBook"]}, {"name": "account", "data_type": "string", "is_categorical": false, "description": "The account number or identifier associated with the balance."}, {"name": "meta", "data_type": "null", "is_categorical": false, "description": "Additional metadata related to the balance record, such as source or context."}, {"name": "transaction", "data_type": "string", "is_categorical": false, "description": "Details of the transaction that affected the balance, including type and amount."}, {"name": "balance", "data_type": "float", "is_categorical": false, "description": "The current balance amount for the account."}, {"name": "notes", "data_type": "integer", "is_categorical": false, "description": "Any additional notes or comments regarding the balance or transaction."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the balance record was created."}, {"name": "expireAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the balance record will expire or be archived."}], "sample_documents": "{\n  \"_id\": \"a3f5e6b2c8d9e1f0a4b2c3d4\",\n  \"key\": \"\\u0017\\u001f\\u00d4\\u00e2\\u0018~\\u00cdNI\\u0016\\u00d8\\u00a3\\u0003;\\u00af\\u00eb$\\u001e\\u00d3v\",\n  \"rawKey\": \"SampleBook;1234abcd5678efghijklmnop:USD\",\n  \"book\": \"SampleBook\",\n  \"account\": \"1234abcd5678efghijklmnop:USD\",\n  \"meta\": null,\n  \"transaction\": \"a1b2c3d4e5f67890abcdef12\",\n  \"balance\": ***********.0,\n  \"notes\": 456,\n  \"createdAt\": \"*************\",\n  \"expireAt\": \"*************\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "cryptotxes", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "people", "description": "This collection stores detailed information about individuals, including their contact information, devices, and security status. This table contains the following fields: _id, name, emails, phones, devices, locations, currentCountry, currentEmail, currentPhone, currentDeviceInfo, isSecurityFlagged, idDocuments, addresses, paymentMethods, flags, createdAt, updatedAt, __v, kyc, id, searchId, tierInfo, searchEmail, limits, externalAccounts.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "name", "data_type": "object", "is_categorical": false, "description": "The full name of the individual.", "nested_fields": [{"name": "name.first<PERSON><PERSON>", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "name.last<PERSON><PERSON>", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "name.fullName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "name.userName", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "emails", "data_type": "array", "is_categorical": false, "description": "An array of email addresses associated with the individual.", "array_items": [{"name": "emails[].emailAddress", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "emails[].emailType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "emails[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "emails[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "phones", "data_type": "array", "is_categorical": false, "description": "An array of phone numbers associated with the individual.", "array_items": [{"name": "phones[].phoneNumber", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "phones[].phoneType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "phones[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "phones[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "phones[].status", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "devices", "data_type": "array", "is_categorical": false, "description": "An array of devices used by the individual.", "array_items": [{"name": "devices[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "locations", "data_type": "array", "is_categorical": false, "description": "An array of geographical locations associated with the individual.", "array_items": [{"name": "locations[].ipAddress", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "locations[].timestamp", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "currentCountry", "data_type": "string", "is_categorical": true, "description": "The current country of residence of the individual.", "found_categorical_values": ["ng", "NG", "US", "us", "GB", "KE", "UG", "CA", "GH", "FR", "gb", "gh", "ug", "DE", "RW", "CI", "ke", "ET", "ca", "EG", "RO", "za", "ZA", "cm", "ES", "CN", "cy", "tr", "BE", "ZM", "CM"]}, {"name": "currentEmail", "data_type": "string", "is_categorical": false, "description": "The primary email address currently in use by the individual."}, {"name": "currentPhone", "data_type": "string", "is_categorical": false, "description": "The primary phone number currently in use by the individual."}, {"name": "currentDeviceInfo", "data_type": "object", "is_categorical": false, "description": "Information about the device currently being used by the individual.", "nested_fields": [{"name": "currentDeviceInfo.isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "None"]}, {"name": "currentDeviceInfo.isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None"]}, {"name": "currentDeviceInfo.createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "currentDeviceInfo.updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "isSecurityFlagged", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating if the individual has been flagged for security reasons.", "found_categorical_values": ["False", "True", "None"]}, {"name": "idDocuments", "data_type": "array", "is_categorical": false, "description": "An array of identification documents provided by the individual."}, {"name": "addresses", "data_type": "array", "is_categorical": false, "description": "An array of physical addresses associated with the individual."}, {"name": "paymentMethods", "data_type": "array", "is_categorical": false, "description": "An array of payment methods linked to the individual."}, {"name": "flags", "data_type": "array", "is_categorical": false, "description": "An array of flags indicating various statuses or attributes of the individual."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the document was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used for version control.", "found_categorical_values": ["0"]}, {"name": "kyc", "data_type": "object", "is_categorical": false, "description": "Duplicate entry for Know Your Customer information related to the individual.", "nested_fields": [{"name": "kyc.dateOfBirth", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "kyc.status", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["notStarted", "pending", "success", "started", "submitted", "failure", "rejected", "reuploadRequested", "reuploaded", "None", "verified"]}, {"name": "kyc.country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "NG", "US", "GH", "GB", "CA", "KE", "UG", "DE", "CM"]}]}, {"name": "id", "data_type": "string", "is_categorical": false, "description": "A secondary identifier for the individual."}, {"name": "searchId", "data_type": "string", "is_categorical": false, "description": "An identifier used for search operations related to the individual."}, {"name": "tierInfo", "data_type": "object", "is_categorical": false, "description": "Duplicate entry for information about the tier or level of service associated with the individual.", "nested_fields": [{"name": "tierInfo.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "tierInfo.transactionCount", "data_type": "integer", "is_categorical": false, "description": ""}]}, {"name": "searchEmail", "data_type": "string", "is_categorical": false, "description": "An email address used for search operations related to the individual."}, {"name": "devices", "data_type": "array", "is_categorical": false, "description": "An array of devices used by the individual.", "array_items": [{"name": "devices[].deviceId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "currentCountry", "data_type": "string", "is_categorical": true, "description": "The current country of residence of the individual.", "found_categorical_values": ["ng", "NG", "US", "us", "CA", "UG", "KE", "GB", "GH", "FR", "gb", "gh", "BE", "ZA", "ug", "CI", "CM", "EG", "DE", "RW", "it", "CN", "tr", "EE", "ET", "cy", "ca", "ES", "cm"]}, {"name": "currentDeviceInfo", "data_type": "object", "is_categorical": false, "description": "Information about the device currently being used by the individual.", "nested_fields": [{"name": "currentDeviceInfo.deviceId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "currentDeviceInfo.deviceType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "ios", "android", ""]}, {"name": "currentDeviceInfo.deviceToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "currentDeviceInfo.isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "None"]}, {"name": "currentDeviceInfo.isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None"]}, {"name": "currentDeviceInfo.createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "currentDeviceInfo.updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "limits", "data_type": "object", "is_categorical": false, "description": "Limits associated with the individual's account or services.", "nested_fields": [{"name": "limits.perDailyLimit", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["3000", "None", "1000", "5000", "0", "1200", "3000000"]}, {"name": "limits.perTransactionLimit", "data_type": "integer", "is_categorical": false, "description": ""}]}, {"name": "externalAccounts", "data_type": "object", "is_categorical": false, "description": "An array of external accounts linked to the individual.", "nested_fields": [{"name": "externalAccounts.stripe", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "externalAccounts.stripe.customerId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "externalAccounts.stripe.accountId", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "kyc", "data_type": "object", "is_categorical": false, "description": "Duplicate entry for Know Your Customer information related to the individual.", "nested_fields": [{"name": "kyc.dateOfBirth", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "kyc.status", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["notStarted", "pending", "success", "started", "submitted", "rejected", "failure", "verified", "None"]}, {"name": "kyc.country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "NG", "US", "CA", "GB", "KE", "GH", "UG", "DE", "CY", "IT", "TR"]}]}, {"name": "tierInfo", "data_type": "object", "is_categorical": false, "description": "Duplicate entry for information about the tier or level of service associated with the individual.", "nested_fields": [{"name": "tierInfo.id", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["64f48c7f2fe5f0cadb400a5d", "None", "66a364f60e43d1eb8d76f4d0", "66a367980e43d1eb8d76f4f8", "67d1a26bbc36bc4c28522dd2", "64f48d4d1137aa9518b00e91", "64f710f05906287b1add9cd5", "6572375ca1021c763b660771", "652f920b3ee37bd2aad5fa3d"]}, {"name": "tierInfo.transactionCount", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "None", "3", "4", "1000", "2", "999"]}]}], "sample_documents": "{\n  \"_id\": \"5f6b3e4a2c3d4e5f67890abc\",\n  \"name\": {\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"fullName\": \"<PERSON>\",\n    \"userName\": \"xzy-45\"\n  },\n  \"emails\": [\n    {\n      \"emailAddress\": \"<EMAIL>\",\n      \"emailType\": \"personal\",\n      \"isDefault\": true,\n      \"isDeactivated\": false\n    }\n  ],\n  \"phones\": [\n    {\n      \"phoneNumber\": \"+**********1\",\n      \"phoneType\": \"mobile\",\n      \"isDefault\": true,\n      \"isDeactivated\": false,\n      \"status\": \"active\"\n    }\n  ],\n  \"devices\": [\n    {\n      \"isDefault\": true,\n      \"isDeactivated\": false,\n      \"createdAt\": \"Tue Apr 25 2023 14:30:00 GMT+0100 (West Africa Standard Time)\",\n      \"updatedAt\": \"2023-04-25T13:30:00\"\n    }\n  ],\n  \"locations\": [\n    {\n      \"ipAddress\": \"***********\",\n      \"timestamp\": \"2023-06-15T10:00:00.000000\"\n    }\n  ],\n  \"currentCountry\": \"us\",\n  \"currentEmail\": \"<EMAIL>\",\n  \"currentPhone\": \"+**********1\",\n  \"currentDeviceInfo\": {\n    \"isDefault\": true,\n    \"isDeactivated\": false,\n    \"createdAt\": \"Tue Apr 25 2023 14:30:00 GMT+0100 (West Africa Standard Time)\",\n    \"updatedAt\": \"2023-04-25T13:30:00\"\n  },\n  \"isSecurityFlagged\": false,\n  \"idDocuments\": [],\n  \"addresses\": [],\n  \"paymentMethods\": [],\n  \"flags\": [],\n  \"createdAt\": \"2023-04-25T13:30:00.131000\",\n  \"updatedAt\": \"2023-09-15T11:00:00.018000\",\n  \"__v\": 0,\n  \"kyc\": {\n    \"dateOfBirth\": \"null\",\n    \"status\": \"inProgress\",\n    \"country\": \"US\"\n  },\n  \"id\": \"5f6b3e4a2c3d4e5f67890abc\",\n  \"searchId\": \"5f6b3e4a2c3d4e5f67890abc\",\n  \"tierInfo\": {\n    \"id\": \"78g90h1j2k3l4m5n6o7p8q9r\",\n    \"transactionCount\": 5\n  },\n  \"searchEmail\": \"<EMAIL>\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "reviews", "description": "This collection stores user reviews for products or services. It allows businesses to gather feedback and ratings from customers, which can be used to improve offerings and enhance customer satisfaction. This table contains the following fields: _id, userId, rating, comment, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each review, automatically generated by MongoDB."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who submitted the review, linking it to the user collection."}, {"name": "rating", "data_type": "integer", "is_categorical": true, "description": "A numerical rating given by the user, typically on a scale from 1 to 5.", "found_categorical_values": ["3", "1", "2"]}, {"name": "comment", "data_type": "string", "is_categorical": false, "description": "The text of the review provided by the user, containing their feedback and opinions."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the review was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the review was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to track document revisions.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"7f3a2b9d4c8e1f2a3b4c5d6e\",\n  \"userId\": \"8a1b2c3d4e5f6g7h8i9j0k1l2\",\n  \"rating\": 4,\n  \"comment\": \"Lorem ipsum dolor sit amet\",\n  \"createdAt\": \"2024-02-15T10:30:45.123000\",\n  \"updatedAt\": \"2024-02-15T10:30:45.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "medici_journals", "description": "This collection stores journal entries related to financial transactions and bookkeeping for the <PERSON> family. It serves as a historical record of their financial activities, documenting various transactions and notes associated with their business dealings.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each journal entry."}, {"name": "memo", "data_type": "string", "is_categorical": true, "description": "A brief note or description associated with the journal entry.", "found_categorical_values": ["withdraw", "swap", "transfer", "deposit", "withdrawal", "Admin Ledger Action", "<PERSON><PERSON><PERSON> failed", "VIRTUAL_CARD_LOAD"]}, {"name": "_transactions", "data_type": "array", "is_categorical": false, "description": "An array of transaction objects related to this journal entry."}, {"name": "datetime", "data_type": "string", "is_categorical": false, "description": "The date and time when the journal entry was created."}, {"name": "book", "data_type": "string", "is_categorical": true, "description": "The name or identifier of the book to which this journal entry belongs.", "found_categorical_values": ["AfriexBook"]}, {"name": "memo", "data_type": "string", "is_categorical": true, "description": "A brief note or description associated with the journal entry.", "found_categorical_values": ["withdraw", "swap", "transfer", "deposit", "withdrawal", "Admin Ledger Action", "Update wallet balance after payment for 6421ccba5765f9e0ab97c59b", "<PERSON><PERSON><PERSON> failed", "Update wallet balance after payment for 6430443eced0e906755eb2d5"]}, {"name": "void_reason", "data_type": "string", "is_categorical": true, "description": "The reason for voiding the journal entry, if applicable.", "found_categorical_values": ["None", "<PERSON><PERSON><PERSON> failed"]}, {"name": "voided", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the journal entry has been voided.", "found_categorical_values": ["None", "True"]}], "sample_documents": "{\n  \"_id\": \"74d9f1a2d3e4f5678a9b0c1d2\",\n  \"memo\": \"Withdrawal for 7f8e9d0c1b2a3e4f5g6h7i8j9\",\n  \"_transactions\": [\n    \"74d9f1a2d3e4f5678a9b0c1d3\",\n    \"74d9f1a2d3e4f5678a9b0c1d4\"\n  ],\n  \"datetime\": \"2023-02-25T09:15:30.123000\",\n  \"book\": \"DummyBook\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "telemetries", "description": "This collection stores telemetry data collected from various devices, capturing performance metrics and operational statistics over time. This table contains the following fields: _id, description, tags, meta, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each telemetry record, automatically generated by MongoDB."}, {"name": "description", "data_type": "string", "is_categorical": false, "description": "A textual description of the telemetry data, providing context or additional information."}, {"name": "tags", "data_type": "array", "is_categorical": false, "description": "An array of tags associated with the telemetry data, used for categorization and filtering."}, {"name": "meta", "data_type": "object", "is_categorical": false, "description": "An object containing metadata about the telemetry data, such as source, version, or other relevant details.", "nested_fields": [{"name": "meta.targetId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.rate", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.fromSymbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["USD", "GBP", "XOF"]}, {"name": "meta.toSymbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "GHS", "KES"]}, {"name": "meta.admin", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "meta.admin.email", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.admin.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.admin.userId", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["622b244cc772b1120bd60141", ""]}]}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the telemetry record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating the last time the telemetry record was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to track document revisions.", "found_categorical_values": ["0"]}, {"name": "meta", "data_type": "object", "is_categorical": false, "description": "An object containing metadata about the telemetry data, such as source, version, or other relevant details.", "nested_fields": [{"name": "meta.targetId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.rate", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.fromSymbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["USD", "XOF", "GBP"]}, {"name": "meta.toSymbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "GHS", "KES"]}, {"name": "meta.admin", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "meta.admin.email", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["<EMAIL>", ""]}, {"name": "meta.admin.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.admin.userId", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["622b244cc772b1120bd60141", ""]}]}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"description\": \"Sample OTC volume created\",\n  \"tags\": [\n    \"USER\",\n    \"GENERATED\",\n    \"OTC_VOLUME\",\n    \"a1b2c3d4e5f67890abcdef11\"\n  ],\n  \"meta\": {\n    \"targetId\": \"a1b2c3d4e5f67890abcdef11\",\n    \"rate\": \"1234.567890123456\",\n    \"fromSymbol\": \"EUR\",\n    \"toSymbol\": \"JPY\",\n    \"admin\": {\n      \"email\": \"<EMAIL>\",\n      \"name\": \"<PERSON><PERSON><PERSON>\",\n      \"userId\": \"random_user_id_987654\"\n    }\n  },\n  \"createdAt\": \"2025-04-01T12:00:00.000000\",\n  \"updatedAt\": \"2025-04-01T12:00:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "pushnotifications", "description": "This collection stores information related to push notifications sent to users. It tracks the status and details of each notification, allowing for efficient management and retrieval of notification data.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each push notification document."}, {"name": "masspush", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the notification was sent as a mass push to multiple users.", "found_categorical_values": ["False", "True"]}, {"name": "read", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the notification has been read by the recipient.", "found_categorical_values": ["False"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of notification, which categorizes the notification (e.g., alert, reminder, promotional).", "found_categorical_values": ["Processing Transaction", "Withdrawal Processed", "<PERSON><PERSON><PERSON> failed", "Transfer successful", "Transfer failed", "test", "Afriex Security Notification", "Withdrawal Failed", "<PERSON>", "Review Status", "Transaction Processed", "Warning!!", "Transaction Successful", "Account Credited"]}, {"name": "info", "data_type": "string", "is_categorical": false, "description": "Additional information or payload associated with the notification."}, {"name": "key", "data_type": "string", "is_categorical": false, "description": "A unique key associated with the notification for tracking purposes."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the notification was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the notification was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of notification, which categorizes the notification (e.g., alert, reminder, promotional).", "found_categorical_values": ["Processing Transaction", "Withdrawal Processed", "<PERSON><PERSON><PERSON> failed", "Transfer successful", "Transfer failed", "test", "Withdrawal Failed", "Afriex Security Notification", "Review Status", "Transaction Processed", "Update Your App ", "Account Credited"]}], "sample_documents": "{\n  \"_id\": \"7a3f1b2c4d5e6789f0a1b2c3\",\n  \"masspush\": true,\n  \"read\": true,\n  \"type\": \"Completed Transaction\",\n  \"info\": \"Your transfer of 4500<NAME_EMAIL> is being processed.\",\n  \"key\": \"1234abcd\",\n  \"createdAt\": \"2022-05-15T14:30:45.123000\",\n  \"updatedAt\": \"2022-05-15T14:30:45.123000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "medici_transactions", "description": "This collection stores financial transaction records for the Medici system, capturing details about each transaction for accounting and auditing purposes. This table contains the following fields: _id, _journal, account_path, accounts, book, credit, datetime, debit, memo, timestamp, meta, void_reason, voided.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each transaction record."}, {"name": "_journal", "data_type": "string", "is_categorical": false, "description": "The journal entry associated with the transaction."}, {"name": "account_path", "data_type": "array", "is_categorical": false, "description": "The hierarchical path of the account involved in the transaction."}, {"name": "accounts", "data_type": "string", "is_categorical": false, "description": "An array of accounts that are affected by the transaction."}, {"name": "book", "data_type": "string", "is_categorical": true, "description": "The accounting book in which the transaction is recorded.", "found_categorical_values": ["AfriexBook"]}, {"name": "credit", "data_type": "integer", "is_categorical": false, "description": "The amount credited in the transaction."}, {"name": "datetime", "data_type": "string", "is_categorical": false, "description": "The date and time when the transaction occurred."}, {"name": "debit", "data_type": "integer", "is_categorical": false, "description": "The amount debited in the transaction."}, {"name": "memo", "data_type": "string", "is_categorical": true, "description": "A note or description related to the transaction.", "found_categorical_values": ["withdraw", "deposit", "swap", "transfer", "withdrawal", "Admin Ledger Action", "<PERSON><PERSON><PERSON> failed", "Update wallet balance after payment for 643009a0675f8d607d0a129f", "Update wallet balance after payment for 6430443eced0e906755eb2d5", "Deposit from Korapay", "Swapping for 6421cc3a5765f9e0ab97c55c from USD to NGN", "Swapping for 611cd7a2c87f2f0821782989 from USD to NGN"]}, {"name": "timestamp", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the transaction record was created or modified."}, {"name": "meta", "data_type": "object", "is_categorical": false, "description": "Additional metadata related to the transaction.", "nested_fields": [{"name": "meta.senderId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.receiverId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "meta.transactionId", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "void_reason", "data_type": "string", "is_categorical": true, "description": "The reason for voiding the transaction, if applicable.", "found_categorical_values": ["None", "User withdrawal failed"]}, {"name": "voided", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the transaction has been voided.", "found_categorical_values": ["None", "True"]}, {"name": "memo", "data_type": "string", "is_categorical": false, "description": "A note or description related to the transaction."}], "sample_documents": "{\n  \"_id\": \"74d5027fbbaf642a297dbd5e\",\n  \"_journal\": \"74d5027e33a3bfc2c49feb20\",\n  \"account_path\": [\n    \"72ab0e3d8212e60c6648de52\",\n    \"EUR\"\n  ],\n  \"accounts\": \"72ab0e3d8212e60c6648de52:EUR\",\n  \"book\": \"DummyBook\",\n  \"credit\": 27,\n  \"datetime\": \"2023-02-20T10:15:45.123000\",\n  \"debit\": 5,\n  \"memo\": \"Deposit for 70fecadec52fef0dea2e2ad3\",\n  \"timestamp\": \"2023-02-20T10:15:44.123000\",\n  \"meta\": {\n    \"senderId\": \"70fecadec52fef0dea2e2ad3\",\n    \"receiverId\": \"72ab0e3d8212e60c6648de52\",\n    \"transactionId\": \"74d5027d33a3bfc2c49feb1c\"\n  },\n  \"void_reason\": \"Transaction not completed\",\n  \"voided\": false\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "bonus", "description": "This collection stores information about bonuses awarded to users, including details about the amount, type, and payment method used for the bonus. This table contains the following fields: _id, userId, amount, type, paymentMethodId, createdAt, updatedAt, __v. The purpose of this collection is to track bonuses given to users, ensuring accurate record-keeping and facilitating financial reporting.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each bonus record, automatically generated by MongoDB."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who is receiving the bonus."}, {"name": "amount", "data_type": "object", "is_categorical": false, "description": "The monetary value of the bonus awarded to the user.", "nested_fields": [{"name": "amount.currencyCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["USD"]}, {"name": "amount.value", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["1"]}]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The category or type of bonus, such as 'cash', 'credit', or 'reward'.", "found_categorical_values": ["add_card"]}, {"name": "paymentMethodId", "data_type": "string", "is_categorical": false, "description": "The identifier for the payment method used to distribute the bonus."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the bonus record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the bonus record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to track document revisions.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"5f4e3c2b1a2d4e5f8b9a0c1d\",\n  \"userId\": \"5f4e3c2b1a2d4e5f8b9a0c2e\",\n  \"amount\": {\n    \"currencyCode\": \"EUR\",\n    \"value\": \"10\"\n  },\n  \"type\": \"remove_card\",\n  \"paymentMethodId\": \"XyZtH7J8gBvuoPqR\",\n  \"createdAt\": \"2024-06-15T12:30:45.123000\",\n  \"updatedAt\": \"2024-06-15T12:30:45.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "client-notifications", "description": "This collection stores notifications sent to clients, allowing for tracking and management of notification messages. This table contains the following fields: _id, message, meta, expiresAt, createdAt, updatedAt, __v. The purpose of this collection is to facilitate the delivery and lifecycle management of notifications, ensuring clients receive timely updates and information.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each notification document, automatically generated by MongoDB."}, {"name": "message", "data_type": "string", "is_categorical": false, "description": "The content of the notification message that will be displayed to the client."}, {"name": "meta", "data_type": "object", "is_categorical": false, "description": "An object containing additional metadata related to the notification, such as type or priority.", "nested_fields": [{"name": "meta.currency", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "NGN", "GHS", "XAF", "KES"]}, {"name": "meta.transactionType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["WITHDRAW"]}, {"name": "meta.processorName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["DLOCAL", "CELLULANT", "HUB2"]}]}, {"name": "expiresAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the notification should expire and no longer be relevant."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the notification was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating the last time the notification was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to manage document versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890123456789\",\n  \"message\": \"We are currently experiencing delays on EUR-->USD with a completion time of 15 mins\",\n  \"meta\": {\n    \"currency\": \"USD\",\n    \"transactionType\": \"DEPOSIT\",\n    \"processorName\": \"PAYPAL\"\n  },\n  \"expiresAt\": \"2025-07-15T12:45:10.123000\",\n  \"createdAt\": \"2025-07-15T12:00:00.000000\",\n  \"updatedAt\": \"2025-07-15T12:00:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "onboardings", "description": "This collection stores information related to user onboarding processes, capturing essential details about users as they register and set up their accounts. This table contains the following fields: _id, phone, country, devices, otp, isDeactivated, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each onboarding record, automatically generated by MongoDB."}, {"name": "phone", "data_type": "string", "is_categorical": false, "description": "The user's phone number, used for communication and verification purposes."}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the user is located, which may influence service availability and compliance.", "found_categorical_values": ["NG", "US"]}, {"name": "devices", "data_type": "array", "is_categorical": false, "description": "An array of devices associated with the user, indicating the platforms they use to access the service.", "array_items": [{"name": "devices[].deviceId", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceCountry", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].appVersion", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceIp", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "otp", "data_type": "object", "is_categorical": false, "description": "A one-time password generated for user verification during the onboarding process.", "nested_fields": [{"name": "otp.codes", "data_type": "array", "is_categorical": false, "description": "", "array_items": [{"name": "otp.codes[].shortCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "otp.codes[].longCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "otp.codes[].channel", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "otp.nonce", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "otp.expiresAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "otp.attempts", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0"]}, {"name": "otp.isUsed", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False"]}]}, {"name": "isDeactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating whether the user's onboarding status is active or has been deactivated.", "found_categorical_values": ["None", "False"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the onboarding record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating the last time the onboarding record was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to track document revisions.", "found_categorical_values": ["0"]}, {"name": "devices", "data_type": "array", "is_categorical": false, "description": "An array of devices associated with the user, indicating the platforms they use to access the service.", "array_items": [{"name": "devices[].deviceId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceCountry", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].appVersion", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceIp", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"phone\": \"+23490123456789\",\n  \"country\": \"GH\",\n  \"devices\": [\n    {\n      \"deviceId\": \"B1C2D3E4-F5A6-7B8C-9D0E-F1G2H3I4J5K6\",\n      \"deviceType\": \"android\",\n      \"deviceToken\": \"\",\n      \"deviceName\": \"Samsung Galaxy S21\",\n      \"deviceCountry\": \"GB\",\n      \"appVersion\": \"12.0.0\",\n      \"deviceIp\": \"********\",\n      \"isDefault\": true,\n      \"isDeactivated\": false,\n      \"createdAt\": \"Mon Apr 05 2025 10:30:00 GMT+0100 (West Africa Standard Time)\",\n      \"updatedAt\": \"2025-04-05T09:30:00\"\n    }\n  ],\n  \"otp\": {\n    \"codes\": [\n      {\n        \"shortCode\": \"654321\",\n        \"longCode\": \"$2b$10$4RRTYUIOPQWERTYUIOPASDFGHJKLZXCVBNMASDFG<PERSON><PERSON><PERSON><PERSON>\",\n        \"channel\": \"email\"\n      }\n    ],\n    \"nonce\": \"f2e3d4c5-b6a7-8c9d-e0f1-23456789abcd\",\n    \"expiresAt\": \"2025-04-05T10:45:56.930000\",\n    \"attempts\": 1,\n    \"isUsed\": false\n  },\n  \"isDeactivated\": false,\n  \"createdAt\": \"2025-04-05T09:30:00.071000\",\n  \"updatedAt\": \"2025-04-05T09:30:00.071000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "bankrates", "description": "This collection stores the latest bank rates for various currencies and financial instruments. It provides a comprehensive view of exchange rates and other financial metrics sourced from different financial institutions.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each document in the collection."}, {"name": "quote", "data_type": "string", "is_categorical": true, "description": "The specific financial quote or rate being represented.", "found_categorical_values": ["UGX", "SRD", "KHR", "VND", "GYD", "NIO", "MKD", "SOS", "GMD", "JEP", "QAR", "OMR", "BZD", "MAD", "ISK", "UYU", "ZMW", "KWD", "BTC", "DJF", "RON", "RSD", "HTG", "MDL", "XCD", "DKK", "XAG", "MUR", "GHS", "SCR", "PEN", "UZS", "MYR", "NAD", "PLN", "PYG", "IRR", "BOB", "CDF", "RWF", "MRO", "CHF", "CUP", "TMT", "IMP", "TRY", "CLF", "SBD", "KMF", "SHP"]}, {"name": "base", "data_type": "string", "is_categorical": true, "description": "The base currency from which the rate is quoted.", "found_categorical_values": ["ETB"]}, {"name": "symbol", "data_type": "string", "is_categorical": false, "description": "The symbol representing the currency or financial instrument."}, {"name": "source", "data_type": "string", "is_categorical": true, "description": "The source from which the rate was obtained, such as a financial institution or market.", "found_categorical_values": ["CURRENCY_LAYER"]}, {"name": "value", "data_type": "string", "is_categorical": false, "description": "The numerical value of the rate or quote."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by Mon<PERSON>ose to track document revisions.", "found_categorical_values": ["0"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the document was last updated."}, {"name": "quote", "data_type": "string", "is_categorical": true, "description": "The specific financial quote or rate being represented.", "found_categorical_values": ["ZMW", "UZS", "BGN", "BIF", "KMF", "LAK", "TND", "INR", "IQD", "LSL", "HNL", "CVE", "KRW", "GIP", "MRO", "BWP", "CHF", "SBD", "CNY", "LBP", "OMR", "XAF", "BRL", "CRC", "KYD", "MYR", "RUB", "PGK", "PAB", "KWD", "KPW", "YER", "QAR", "TWD", "GHS", "IDR", "GMD", "HTG", "VND", "PYG", "MOP", "MZN", "FJD", "KHR", "BZD", "MNT", "SCR", "AOA", "GTQ", "MAD"]}], "sample_documents": "{\n  \"_id\": \"f3b2c4d5e6f7g8h9i0j1k2l3m\",\n  \"quote\": \"XYZ\",\n  \"base\": \"USD\",\n  \"symbol\": \"USDXYZ\",\n  \"source\": \"FAKE_SOURCE\",\n  \"value\": \"15.678912\",\n  \"__v\": 0,\n  \"createdAt\": \"2023-10-15T08:00:00.158000\",\n  \"updatedAt\": \"2023-10-15T08:00:00.158000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "paymentmethods", "description": "This collection stores information about various payment methods available for users. It allows tracking of user payment preferences and their associated details.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each payment method document."}, {"name": "channel", "data_type": "string", "is_categorical": true, "description": "The payment channel through which the transaction is processed (e.g., credit card, PayPal).", "found_categorical_values": ["BANK_ACCOUNT", "MOBILE_MONEY", "CARD", "ACH_BANK_ACCOUNT", "INTERAC"]}, {"name": "capabilities", "data_type": "array", "is_categorical": false, "description": "A list of capabilities supported by the payment method (e.g., refunds, recurring payments)."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user associated with this payment method."}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor used to handle transactions for this payment method.", "found_categorical_values": ["FAIRMONEY", "ALPAY", "PROVIDUS", "CELLULANT", "STRIPE", "ZILMONEY", "MONIEPOINT", "TERRAPAY", "JUICYWAY", "HUB2", "DWOLLA"]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the payment method (e.g., active, inactive).", "found_categorical_values": ["deleted", "active"]}, {"name": "info", "data_type": "object", "is_categorical": false, "description": "Additional information or metadata related to the payment method.", "nested_fields": [{"name": "info.accountNumber", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "info.accountName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "info.accountPhone", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "info.bankName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["First Bank", "None", "WEMA BANK", "Access Bank", "GTBank", "Cellulant", "Chase Bank", "Citi Bank", "Regions Bank", "BANK OF BARODA LTD", "Chase", "Wells Fargo", "STRIPE TEST BANK", "EQUITY BANK", "FCMB", "US Test Bank", "UBA BANK"]}, {"name": "info.bankCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["000016", "None", "000017", "000014", "000010", "000015", "000005", "0035", "", "000003", "090267", "000007", "000006", "000009", "100005"]}, {"name": "info.currency", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "GHS", "UGX", "USD", "KES", "GBP", "CAD", "XAF", "EUR", "XOF"]}, {"name": "info.country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NG", "GH", "US", "UG", "KE", "GB", "CA", "CM"]}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the payment method was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the payment method was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used for versioning in MongoDB.", "found_categorical_values": ["0"]}, {"name": "channel", "data_type": "string", "is_categorical": true, "description": "The payment channel through which the transaction is processed (e.g., credit card, PayPal).", "found_categorical_values": ["BANK_ACCOUNT", "MOBILE_MONEY", "CARD", "INTERAC"]}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor used to handle transactions for this payment method.", "found_categorical_values": ["FAIRMONEY", "ALPAY", "PROVIDUS", "CELLULANT", "STRIPE", "ZEEPAY", "ZILMONEY", "DWOLLA", "TERRAPAY", "JUICYWAY"]}, {"name": "info", "data_type": "object", "is_categorical": false, "description": "Additional information or metadata related to the payment method.", "nested_fields": [{"name": "info.accountNumber", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "info.accountName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "info.accountPhone", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "info.bankName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["First Bank", "None", "WEMA BANK", "Access Bank", "Wells Fargo", "Huntington Bank", "FCMB", "UBA BANK", "Access Bank (Diamond)", "ACCESS BANK", "Keystone Bank", "ECOBANK", "Zenith Bank", "PROVIDUS BANK", "Citi Bank", "New Prudential Bank"]}, {"name": "info.bankCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["000016", "None", "000017", "000014", "000013", "000005", "000009", "000010", "000007", "000020", "000001", "CBETETAA", "000002", "UGBAUGKA", "***********", "000015", "000008"]}, {"name": "info.currency", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "GHS", "USD", "UGX", "KES", "CAD"]}, {"name": "info.country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NG", "GH", "UG", "US", "KE", "CA", "GB", "CM", "PK", "DE"]}]}], "sample_documents": "{\n  \"_id\": \"f3a1b2c3d4e5f67890123456\",\n  \"channel\": \"BANK_ACCOUNT\",\n  \"capabilities\": [\n    \"WITHDRAW\"\n  ],\n  \"userId\": \"a1b2c3d4e5f6789012345678\",\n  \"processor\": \"FINANCIAL_SERVICES\",\n  \"status\": \"deleted\",\n  \"info\": {\n    \"accountNumber\": \"**********\",\n    \"accountName\": \"Sample Account\",\n    \"accountPhone\": \"\",\n    \"bankName\": \"Global Bank\",\n    \"bankCode\": \"000001\",\n    \"currency\": \"USD\",\n    \"country\": \"US\"\n  },\n  \"createdAt\": \"2023-07-10T12:00:00.000000\",\n  \"updatedAt\": \"2023-07-10T12:05:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "accounts", "description": "This collection stores user account information for the application. It manages user authentication, referral tracking, and account status, ensuring secure access and user management.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each account document, automatically generated by MongoDB."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "A unique identifier assigned to each user, used for referencing user accounts."}, {"name": "referrer", "data_type": "string", "is_categorical": true, "description": "The userId of the account that referred this user, if applicable.", "found_categorical_values": ["", "tests550", "None", "mamafavour", "stage521", "omoni386", "sefl", "$alexy448", "tolu_influence", "mumu1993…", "afets322", "smile70", "testc130", "caguwa4195", "0183", "damii30", "djst", "tttttt", "b<PERSON><PERSON><PERSON>", "virtu120", "walte464", "danielobirije", "ayoja34", "imusa78", "TundeEnd", "essien2000$", "Omoni386", "sea"]}, {"name": "email", "data_type": "string", "is_categorical": false, "description": "The email address associated with the user account, used for communication and authentication."}, {"name": "phone", "data_type": "string", "is_categorical": false, "description": "The phone number associated with the user account, used for verification and communication."}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the user is located, used for localization and compliance.", "found_categorical_values": ["ng", "us", "NG", "US", "gh", "UG", "KE", "gb", "CA", "GB", "ca", "FR", "ke", "ug", "GH", "CI", "DE", "BE", "CM", "RW", "de", "ZA", "RO", "EG", "ZM", "ET", "TZ", "SN", "ES", "PK"]}, {"name": "userName", "data_type": "string", "is_categorical": false, "description": "The display name chosen by the user for their account."}, {"name": "isAdmin", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the user has administrative privileges.", "found_categorical_values": ["False", "True"]}, {"name": "password", "data_type": "object", "is_categorical": false, "description": "The hashed password for the user account, used for authentication.", "nested_fields": [{"name": "password.hash", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "password.version", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["1"]}, {"name": "password.isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False"]}]}, {"name": "devices", "data_type": "array", "is_categorical": false, "description": "An array of devices associated with the user account, used for tracking access.", "array_items": [{"name": "devices[].deviceId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceCountry", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceIp", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "otp", "data_type": "object", "is_categorical": false, "description": "The one-time password used for two-factor authentication.", "nested_fields": [{"name": "otp.codes", "data_type": "array", "is_categorical": false, "description": "", "array_items": [{"name": "otp.codes[].shortCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "otp.codes[].longCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "otp.codes[].channel", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "otp.nonce", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "otp.expiresAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "otp.attempts", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "None", "3"]}, {"name": "otp.isUsed", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "None", "False"]}]}, {"name": "isDeactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the user account is deactivated.", "found_categorical_values": ["None", "False", "True"]}, {"name": "history", "data_type": "array", "is_categorical": false, "description": "An array of historical actions or changes made to the user account.", "array_items": [{"name": "history[].password", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "history[].password.hash", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "history[].password.version", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "history[].password.isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "history[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "history[]._id", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the account was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating the last time the account was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used by Mongoose for versioning.", "found_categorical_values": ["0", "None"]}, {"name": "referrer", "data_type": "string", "is_categorical": true, "description": "The userId of the account that referred this user, if applicable.", "found_categorical_values": ["", "tests550", "None", "tolu_influence", "mamafavour", "stage521", "sefl", "sixxxxxxx", "Iran0191", "subomi123@", "$alexy448", "larteypatrick5118", "bussy561", "b<PERSON><PERSON><PERSON>", "********", "omoni386", "0183", "munachi04$", "1234", "internal2022@@", "Omoni34", "tttttt", "testc610", "harrison19", "laron976", "yomia796", "walte464", "prayp346", "ezrag162", "djst", "testc130", "sis", "iuuu", "Omoniyi34", "imusa78", "greg", "test", "Omoniyi900000999", "ng2769", "harbor_loreh3$", "sea"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the user is located, used for localization and compliance.", "found_categorical_values": ["ng", "us", "NG", "US", "KE", "gh", "ca", "gb", "GB", "UG", "GH", "CA", "ke", "FR", "ug", "RW", "CI", "BE", "ET", "SN", "ZA", "za", "cy", "ht", "TZ", "EG"]}, {"name": "role", "data_type": "string", "is_categorical": true, "description": "The role assigned to the user, determining their permissions within the application.", "found_categorical_values": ["None", "user", "super_admin", "compliance.manager", "engineering"]}, {"name": "devices", "data_type": "array", "is_categorical": false, "description": "An array of devices associated with the user account, used for tracking access.", "array_items": [{"name": "devices[].deviceId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].deviceName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceCountry", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].deviceIp", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].isDefault", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].isDeactivated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "devices[].createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "devices[].updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "history", "data_type": "array", "is_categorical": false, "description": "An array of historical actions or changes made to the user account."}, {"name": "attempts", "data_type": "array", "is_categorical": false, "description": "The number of failed login attempts for the user account."}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"userId\": \"a1b2c3d4e5f67890abcdef13\",\n  \"referrer\": \"\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+*************\",\n  \"country\": \"US\",\n  \"userName\": \"randomUser123\",\n  \"isAdmin\": false,\n  \"password\": {\n    \"hash\": \"$2b$10$randomHashValue**********\",\n    \"version\": 1,\n    \"isDeactivated\": false\n  },\n  \"devices\": [\n    {\n      \"deviceId\": \"A1B2C3D4-E5F6-7890-ABCD-EF**********\",\n      \"deviceType\": \"\",\n      \"deviceToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.******************************************************************************************************************************************************************************************************.randomString\",\n      \"deviceName\": \"{{RandomDevice}}\",\n      \"deviceCountry\": \"{{RandomDeviceCountry}}\",\n      \"deviceIp\": \"***********\",\n      \"createdAt\": \"Sat Jan 01 2025 12:00:00 GMT+0000 (Coordinated Universal Time)\",\n      \"updatedAt\": \"2025-01-01T12:00:00\"\n    }\n  ],\n  \"otp\": {\n    \"codes\": [\n      {\n        \"shortCode\": \"654321\",\n        \"longCode\": \"$2b$10$randomLongCodeValue**********\",\n        \"channel\": \"sms\"\n      },\n      {\n        \"shortCode\": \"654321\",\n        \"longCode\": \"$2b$10$anotherRandomLongCodeValue**********\",\n        \"channel\": \"email\"\n      }\n    ],\n    \"nonce\": \"12345678-90ab-cdef-1234-567890abcdef\",\n    \"expiresAt\": \"2025-01-01T12:15:00.000000\",\n    \"attempts\": 0,\n    \"isUsed\": false\n  },\n  \"isDeactivated\": false,\n  \"history\": [\n    {\n      \"password\": {\n        \"hash\": \"$2b$10$randomHashValue**********\",\n        \"version\": 1,\n        \"isDeactivated\": false\n      },\n      \"updatedAt\": \"2025-01-01T12:00:05.000000\",\n      \"_id\": \"a1b2c3d4e5f67890abcdef14\"\n    }\n  ],\n  \"createdAt\": \"2025-01-01T12:00:00.000000\",\n  \"updatedAt\": \"2025-01-01T12:00:05.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "accountings", "description": "This collection stores financial transaction records for user accounts, capturing changes in account balances and transaction details. This table contains the following fields: _id, before, after, action, transactionId, amount, currency, userId, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each accounting record, automatically generated by MongoDB."}, {"name": "before", "data_type": "object", "is_categorical": false, "description": "The account balance before the transaction was processed.", "nested_fields": [{"name": "before.symbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "USD", "GHS", "KES", "UGX", "CAD", "GBP"]}, {"name": "before.amount", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "after", "data_type": "object", "is_categorical": false, "description": "The account balance after the transaction has been applied.", "nested_fields": [{"name": "after.symbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "USD", "GHS", "KES", "UGX", "GBP", "USDT", "CAD"]}, {"name": "after.amount", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "action", "data_type": "string", "is_categorical": true, "description": "The type of action performed, such as 'credit' or 'debit'.", "found_categorical_values": ["debit", "credit"]}, {"name": "transactionId", "data_type": "string", "is_categorical": false, "description": "A unique identifier for the transaction associated with this accounting record."}, {"name": "amount", "data_type": "integer", "is_categorical": false, "description": "The monetary amount involved in the transaction."}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the transaction amount is denominated.", "found_categorical_values": ["NGN", "USD", "GHS", "KES", "UGX", "GBP", "CAD", "EUR"]}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user whose account is being affected by the transaction."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the accounting record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the accounting record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to manage document versioning.", "found_categorical_values": ["0"]}, {"name": "before", "data_type": "object", "is_categorical": false, "description": "The account balance before the transaction was processed.", "nested_fields": [{"name": "before.symbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "USD", "GHS", "KES", "UGX", "CAD", "GBP", "EUR"]}, {"name": "before.amount", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "after", "data_type": "object", "is_categorical": false, "description": "The account balance after the transaction has been applied.", "nested_fields": [{"name": "after.symbol", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["NGN", "USD", "GHS", "KES", "UGX", "USDT", "GBP", "CAD"]}, {"name": "after.amount", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the transaction amount is denominated.", "found_categorical_values": ["NGN", "USD", "GHS", "KES", "UGX", "USDT", "CAD", "EUR"]}], "sample_documents": "{\n  \"_id\": \"7f3a1b2c3d4e5f67890abcde\",\n  \"before\": {\n    \"symbol\": \"USD\",\n    \"amount\": \"1234.56\"\n  },\n  \"after\": {\n    \"symbol\": \"USD\",\n    \"amount\": \"1300.00\"\n  },\n  \"action\": \"debit\",\n  \"transactionId\": \"7f3a1b2c3d4e5f67890abcdf\",\n  \"amount\": 45,\n  \"currency\": \"USD\",\n  \"userId\": \"7f3a1b2c3d4e5f67890abcde\",\n  \"createdAt\": \"2022-01-15T10:20:30.123000\",\n  \"updatedAt\": \"2022-01-15T10:20:30.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "dynamicratepromos", "description": "This collection stores promotional rate information for currency exchanges, allowing users to benefit from dynamic rates based on various criteria. This table contains the following fields: _id, originatingCurrency, terminatingCurrency, userLocation, promoType, period, rate, startTime, createdBy, endTime, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "Unique identifier for each promotional rate entry."}, {"name": "originatingCurrency", "data_type": "string", "is_categorical": true, "description": "The currency from which the exchange is initiated.", "found_categorical_values": ["USD", "CAD", "EUR"]}, {"name": "terminatingCurrency", "data_type": "string", "is_categorical": true, "description": "The currency into which the exchange is made.", "found_categorical_values": ["NGN", "KES", "GHS", "EUR"]}, {"name": "userLocation", "data_type": "string", "is_categorical": true, "description": "Geographical location of the user, which may influence the promotional rate.", "found_categorical_values": ["US", "NG", "AU", "GH"]}, {"name": "promoType", "data_type": "string", "is_categorical": false, "description": "Type of promotion being offered (e.g., percentage discount, fixed rate)."}, {"name": "period", "data_type": "integer", "is_categorical": false, "description": "Duration for which the promotional rate is valid."}, {"name": "rate", "data_type": "integer", "is_categorical": true, "description": "The exchange rate offered during the promotional period.", "found_categorical_values": ["None", "2", "1", "5"]}, {"name": "startTime", "data_type": "string", "is_categorical": false, "description": "Timestamp indicating when the promotional rate becomes active."}, {"name": "created<PERSON>y", "data_type": "string", "is_categorical": true, "description": "Identifier for the user or system that created the promotional entry.", "found_categorical_values": ["5eb2bd50718246038e2d00ec", "622b244cc772b1120bd60141", "620cb979eba0391b49975e5c"]}, {"name": "endTime", "data_type": "string", "is_categorical": false, "description": "Timestamp indicating when the promotional rate expires."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "Timestamp indicating when the promotional entry was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "Timestamp indicating the last time the promotional entry was updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "Version key used by MongoDB for internal versioning.", "found_categorical_values": ["0"]}, {"name": "terminatingCurrency", "data_type": "string", "is_categorical": true, "description": "The currency into which the exchange is made.", "found_categorical_values": ["NGN", "GHS", "EUR", "KES"]}, {"name": "rate", "data_type": "integer", "is_categorical": true, "description": "The exchange rate offered during the promotional period.", "found_categorical_values": ["None", "5", "2", "1"]}, {"name": "created<PERSON>y", "data_type": "string", "is_categorical": false, "description": "Identifier for the user or system that created the promotional entry."}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"originatingCurrency\": \"EUR\",\n  \"terminatingCurrency\": \"JPY\",\n  \"userLocation\": \"FR\",\n  \"promoType\": \"Promotion\",\n  \"period\": 45,\n  \"rate\": 7,\n  \"startTime\": \"2023-06-01T00:00:00\",\n  \"createdBy\": \"abcdef**********abcdef12\",\n  \"endTime\": \"2023-06-30T00:00:00\",\n  \"createdAt\": \"2023-06-15T10:20:30.123000\",\n  \"updatedAt\": \"2023-06-15T10:20:30.123000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "bankinfos", "description": "This collection stores information related to bank accounts associated with users. This table contains the following fields: _id, type, user, bankName, account_number, account_name, phoneNumber, bank_code, currency, processor, deactivated, cashPickup, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of bank account (e.g., savings, checking).", "found_categorical_values": ["payout"]}, {"name": "user", "data_type": "string", "is_categorical": false, "description": "The identifier for the user associated with the bank account."}, {"name": "bankName", "data_type": "string", "is_categorical": true, "description": "The name of the bank where the account is held.", "found_categorical_values": ["Zenith Bank", "Access Bank", "Access Bank (Diamond)", "Citi Bank", "ECOBANK", "Cellulant", "FCMB", "First Bank", "Enterprise Bank", "Fidelity Bank", "GTBank", "Heritage", "Providus Bank ", "First City Monument Bank", "JAIZ Bank", "AB Microfinance bank", "Above Only Microfinance bank", "VFD MICROFINANCE BANK", "ABBEY MORTGAGE BANK", "OPAY", "9 Payment Service Bank", "Keystone Bank", "ABU Microfinance bank", "<PERSON><PERSON><PERSON>", "AccessMobile", "POLARIS BANK", "Abucoop Microfinance Bank", "Sterling Bank"]}, {"name": "account_number", "data_type": "string", "is_categorical": false, "description": "The unique account number assigned by the bank."}, {"name": "account_name", "data_type": "string", "is_categorical": false, "description": "The name of the account holder."}, {"name": "phoneNumber", "data_type": "string", "is_categorical": false, "description": "The contact phone number associated with the bank account."}, {"name": "bank_code", "data_type": "string", "is_categorical": true, "description": "The code that identifies the bank (e.g., routing number).", "found_categorical_values": ["000015", "000014", "000005", "000009", "000010", "100005", "000016", "000003", "000019", "000007", "000013", "000023", "000020", "090270", "000006", "090260", "999999", "100004", "070010", "090197", "000002", "000001", "090175", "090424", "000008", "120001", "100013"]}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the account operates.", "found_categorical_values": ["NGN"]}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor associated with the bank account.", "found_categorical_values": ["vfd"]}, {"name": "deactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the account is deactivated.", "found_categorical_values": ["True", "False"]}, {"name": "cashPickup", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating if cash pickup is available for this account.", "found_categorical_values": ["False"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the bank account information was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the bank account information was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by Mongoose to manage document versions.", "found_categorical_values": ["0"]}, {"name": "bankName", "data_type": "string", "is_categorical": true, "description": "The name of the bank where the account is held.", "found_categorical_values": ["Zenith Bank", "Access Bank", "Access Bank (Diamond)", "Citi Bank", "ECOBANK", "Cellulant", "FCMB", "Enterprise Bank", "First Bank", "Fidelity Bank", "Heritage", "GTBank", "Providus Bank ", "AB Microfinance bank", "JAIZ Bank", "First City Monument Bank", "VFD MICROFINANCE BANK", "ABBEY MORTGAGE BANK", "OPAY", "Above Only Microfinance bank", "Abucoop Microfinance Bank", "AccessMobile", "Sterling Bank", "9 Payment Service Bank", "ABU Microfinance bank", "Keystone Bank", "<PERSON><PERSON><PERSON>", "POLARIS BANK"]}, {"name": "bank_code", "data_type": "string", "is_categorical": true, "description": "The code that identifies the bank (e.g., routing number).", "found_categorical_values": ["000015", "000014", "000005", "000009", "000010", "100005", "000016", "000003", "000019", "000007", "000013", "000023", "000020", "090270", "000006", "070010", "100004", "999999", "090260", "090197", "100013", "000001", "000002", "090175", "090424", "120001", "000008"]}], "sample_documents": "{\n  \"_id\": \"73f8c2bffb20a7e98f12345d\",\n  \"type\": \"payout\",\n  \"user\": \"51abcde12345fgh67890ijkl\",\n  \"bankName\": \"First Bank of Nigeria\",\n  \"account_number\": \"**********\",\n  \"account_name\": \"<PERSON>\",\n  \"phoneNumber\": \"\",\n  \"bank_code\": \"000012\",\n  \"currency\": \"USD\",\n  \"processor\": \"xyz\",\n  \"deactivated\": false,\n  \"cashPickup\": false,\n  \"createdAt\": \"2023-09-15T14:45:30.123456\",\n  \"updatedAt\": \"2023-09-15T14:45:30.123456\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "messages", "description": "This collection stores messages exchanged between users in the application. It facilitates communication by keeping track of message details, including the sender, recipient, and message status.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each message document."}, {"name": "title", "data_type": "string", "is_categorical": false, "description": "The title or subject of the message."}, {"name": "description", "data_type": "string", "is_categorical": false, "description": "The main content or body of the message."}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the message (e.g., sent, delivered, read).", "found_categorical_values": ["read"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of message (e.g., text, image, video).", "found_categorical_values": ["info"]}, {"name": "sender", "data_type": "object", "is_categorical": false, "description": "The identifier of the user who sent the message.", "nested_fields": [{"name": "sender.type", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["admin"]}, {"name": "sender.id", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["afriex"]}]}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who is the intended recipient of the message."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the message was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the message was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB for document versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"title\": \"Transaction Update\",\n  \"description\": \"Your transfer of 5.00 <NAME_EMAIL> is being processed.\",\n  \"status\": \"unread\",\n  \"type\": \"alert\",\n  \"sender\": {\n    \"type\": \"user\",\n    \"id\": \"dummyuser\"\n  },\n  \"userId\": \"**********abcdef12345678\",\n  \"createdAt\": \"2023-05-15T14:45:30.123000\",\n  \"updatedAt\": \"2023-09-01T09:20:45.456000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "users-referrals", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "securitynotes", "description": "This collection stores notes related to security incidents or observations made by users. It allows users to document and track security-related information for better awareness and response.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each note, automatically generated by MongoDB."}, {"name": "user", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who created the note, linking it to the user account."}, {"name": "note", "data_type": "string", "is_categorical": false, "description": "The content of the security note, detailing the incident or observation."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the note was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the note was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The category or type of the security note, helping to classify the nature of the incident.", "found_categorical_values": ["None", "card-error", "Blocked"]}], "sample_documents": "{\n  \"_id\": \"5f4e3c2b1a2b4c3d8e9f0a1b\",\n  \"user\": \"7a8b9c0d1e2f3g4h5i6j7k8l\",\n  \"note\": \"Card location greater than distance threshold: 987\",\n  \"createdAt\": \"2022-05-20T14:45:30.456000\",\n  \"updatedAt\": \"2022-05-20T14:45:30.456000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "testtransactions", "description": "This collection stores information about transactions processed through various payment processors. It tracks the details of each transaction, including user information, transaction status, and associated financial data. This table contains the following fields: _id, processor, userId, asset, status, type, amount, country, accountDetails, channel, rejectionReason, __v, createdAt, updatedAt.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each transaction record."}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor used for the transaction.", "found_categorical_values": ["ZEEPAY", "BEYONIC", "ZENITH", "STRIPE", "CELLULANT", "PROVIDUS", "SHUTTERSCORE"]}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who initiated the transaction."}, {"name": "asset", "data_type": "string", "is_categorical": true, "description": "The type of asset involved in the transaction (e.g., currency, stock).", "found_categorical_values": ["GHS", "NGN", "UGX", "KES", "USD"]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the transaction (e.g., completed, pending, failed).", "found_categorical_values": ["REJECTED", "FAILED", "SUCCESS"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of transaction (e.g., debit, credit).", "found_categorical_values": ["WITHDRAW"]}, {"name": "amount", "data_type": "integer", "is_categorical": true, "description": "The monetary amount of the transaction.", "found_categorical_values": ["100", "20", "1000", "50"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the transaction originated.", "found_categorical_values": ["GH", "NG", "KE", "UG", "US"]}, {"name": "accountDetails", "data_type": "object", "is_categorical": false, "description": "Details of the account used for the transaction.", "nested_fields": [{"name": "accountDetails.provider", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "VODAFONE", "MPESA", "AIRTEL", "TELEBIRR"]}, {"name": "accountDetails.phone", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "accountDetails.name", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "channel", "data_type": "string", "is_categorical": true, "description": "The channel through which the transaction was processed (e.g., web, mobile).", "found_categorical_values": ["MOBILE_MONEY", "BANK_ACCOUNT", "CARD"]}, {"name": "rejectionReason", "data_type": "string", "is_categorical": true, "description": "The reason for transaction rejection, if applicable.", "found_categorical_values": ["None", "Error: Request failed with status code 401", "Error: Request failed with status code 500", "Error: Request failed with status code 422", "Error: Failed to get token: INVALID CALLER IP ADDRESS", "StatusCodeError: 500 - {\"detail\":\"An unexpected error has occurred. Please <NAME_EMAIL>. (1205, 'Lock wait timeout exceeded; try restarting transaction')\"}", "AfriexError: Failed to initiate remittance zeepay", "Error: Request failed with status code 504"]}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "Version key for managing document revisions.", "found_categorical_values": ["0"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the transaction record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the transaction record was last updated."}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor used for the transaction.", "found_categorical_values": ["ZEEPAY", "BEYONIC", "SHUTTERSCORE", "STRIPE", "PROVIDUS", "CELLULANT", "ZENITH"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the transaction originated.", "found_categorical_values": ["NG", "GH", "UG", "KE", "US"]}, {"name": "accountDetails", "data_type": "object", "is_categorical": false, "description": "Details of the account used for the transaction.", "nested_fields": [{"name": "accountDetails.provider", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "VODAFONE", "AIRTEL", "MPESA"]}, {"name": "accountDetails.phone", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "accountDetails.name", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "rejectionReason", "data_type": "string", "is_categorical": true, "description": "The reason for transaction rejection, if applicable.", "found_categorical_values": ["None", "Error: Request failed with status code 401", "Error: Request failed with status code 422", "Error: Request failed with status code 500", "Error: Failed to get token: INVALID CALLER IP ADDRESS", "StatusCodeError: 500 - {\"detail\":\"An unexpected error has occurred. Please <NAME_EMAIL>. (1205, 'Lock wait timeout exceeded; try restarting transaction')\"}", "Error: Request failed with status code 502", "Error: Request failed with status code 504"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"processor\": \"PAYSTACK\",\n  \"userId\": \"**********abcdef12345678\",\n  \"asset\": \"USD\",\n  \"status\": \"APPROVED\",\n  \"type\": \"DEPOSIT\",\n  \"amount\": 5000,\n  \"country\": \"US\",\n  \"accountDetails\": {\n    \"provider\": \"VERIZON\",\n    \"phone\": \"+**********1\",\n    \"name\": \"<PERSON>\"\n  },\n  \"channel\": \"BANK_TRANSFER\",\n  \"rejectionReason\": \"null\",\n  \"__v\": 0,\n  \"createdAt\": \"2023-01-01T12:00:00.000000\",\n  \"updatedAt\": \"2023-01-01T12:00:00.000000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "transaction-fees", "description": "This collection stores information about transaction fees applied to various currency pairs in a trading system. This table contains the following fields: _id, percentValue, isActive, type, currencyPair, transactionType, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each transaction fee record, automatically generated by MongoDB."}, {"name": "percentValue", "data_type": "integer", "is_categorical": true, "description": "The percentage value of the transaction fee applied to the transaction.", "found_categorical_values": ["1", "0", "None", "2", "0.5", "0.2", "4"]}, {"name": "isActive", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the transaction fee is currently active or not.", "found_categorical_values": ["True", "False"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of fee, which may categorize the fee into different groups (e.g., fixed, variable).", "found_categorical_values": ["override", "default"]}, {"name": "currencyPair", "data_type": "string", "is_categorical": true, "description": "The trading pair (e.g., BTC/USD) to which the transaction fee applies.", "found_categorical_values": ["None", "ETB:ETB", "CNY:*", "CAD:EUR", "GBP:GBP", "GBP:*", "NGN:NGN", "HTG:HTG", "BRL:BRL", "CAD:*", "CAD:CAD", "BRL:*", "PHP:PHP", "PKR:PKR", "EUR:EUR", "USD:USD", "UGX:UGX", "MXN:MXN", "CNY:CNY", "GHS:USD", "NGN:USD", "GHS:GHS", "XAF:XAF", "EGP:EGP", "GNF:GNF", "INR:INR", "CNY:CAD", "EGP:*", "XOF:XOF", "UYU:UYU", "KES:KES"]}, {"name": "transactionType", "data_type": "string", "is_categorical": true, "description": "The type of transaction (e.g., buy, sell) that the fee is associated with.", "found_categorical_values": ["E2E", "None", "SWAP", "WITHDRAW", "DEPOSIT", "VIRTUAL_CARD_LOAD", "ACH"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the transaction fee record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the transaction fee record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0", "None"]}, {"name": "currencyPair", "data_type": "string", "is_categorical": true, "description": "The trading pair (e.g., BTC/USD) to which the transaction fee applies.", "found_categorical_values": ["None", "ETB:ETB", "CNY:*", "CAD:EUR", "NGN:NGN", "GBP:GBP", "GBP:*", "HTG:HTG", "CAD:CAD", "CAD:*", "BRL:*", "BRL:BRL", "NGN:USD", "XOF:XOF", "GHS:USD", "UGX:UGX", "GHS:GHS", "CNY:CNY", "EGP:EGP", "XAF:XAF", "GNF:GNF", "PKR:PKR", "PHP:PHP", "EUR:EUR", "INR:INR", "CNY:CAD", "MXN:MXN", "EGP:*", "KES:KES", "USD:USD", "UYU:UYU"]}], "sample_documents": "{\n  \"_id\": \"7f3b2e1c4a8b9d2e4f1a0c3d\",\n  \"percentValue\": 0,\n  \"isActive\": false,\n  \"type\": \"normal\",\n  \"currencyPair\": \"USD:EUR\",\n  \"transactionType\": \"B2B\",\n  \"createdAt\": \"2025-01-15T09:30:45.123000\",\n  \"updatedAt\": \"2025-01-15T09:30:45.123000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "transactions", "description": "This collection stores information about financial transactions processed through various channels. It tracks the details of each transaction, including the involved parties, amounts, and statuses.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "Unique identifier for each transaction."}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor handling the transaction.", "found_categorical_values": ["STRIPE", "None", "FAIRMONEY", "ALPAY", "PROVIDUS", "CELLULANT", "DWOLLA", "TERRAPAY", "ZILMONEY"]}, {"name": "processorTransactionId", "data_type": "string", "is_categorical": false, "description": "Identifier assigned by the payment processor for the transaction."}, {"name": "internalTransactionId", "data_type": "string", "is_categorical": false, "description": "Unique identifier for the transaction used internally."}, {"name": "sourceUserId", "data_type": "string", "is_categorical": false, "description": "Identifier for the user initiating the transaction."}, {"name": "sourceAsset", "data_type": "string", "is_categorical": true, "description": "The asset type being sent from the source.", "found_categorical_values": ["USD", "EUR", "NGN", "GBP", "GHS", "CAD", "UGX", "KES"]}, {"name": "sourceAmount", "data_type": "integer", "is_categorical": false, "description": "The amount of the source asset being transferred."}, {"name": "sourceCountry", "data_type": "string", "is_categorical": true, "description": "The country from which the transaction originates.", "found_categorical_values": ["US", "DE", "NG", "GB", "ES", "UG", "GH", "CA"]}, {"name": "destinationAsset", "data_type": "string", "is_categorical": true, "description": "The asset type being received at the destination.", "found_categorical_values": ["NGN", "USD", "GBP", "GHS", "None", "KES", "UGX", "EUR", "CAD", "XAF"]}, {"name": "destinationAmount", "data_type": "float", "is_categorical": false, "description": "The amount of the destination asset being received."}, {"name": "destinationCountry", "data_type": "string", "is_categorical": true, "description": "The country where the transaction is headed.", "found_categorical_values": ["NG", "US", "GB", "GH", "None", "KE", "UG", "ES", "CM", "CA", "ET"]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "Current status of the transaction (e.g., pending, completed, failed).", "found_categorical_values": ["SUCCESS", "PENDING", "PROCESSING", "UNKNOWN", "REFUNDED"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "Type of transaction (e.g., transfer, payment, refund).", "found_categorical_values": ["WITHDRAW", "DEPOSIT", "SWAP", "TRANSFER"]}, {"name": "destinationAccountDetails", "data_type": "object", "is_categorical": false, "description": "Details of the account receiving the funds.", "nested_fields": [{"name": "destinationAccountDetails.accountNumber", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "destinationAccountDetails.bankName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "First Bank", "WEMA BANK"]}, {"name": "destinationAccountDetails.accountName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "destinationAccountDetails.phoneNumber", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", ""]}, {"name": "destinationAccountDetails.bankCode", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "000016", "000017", "000003", "000009", "000007"]}]}, {"name": "channel", "data_type": "string", "is_categorical": true, "description": "The channel through which the transaction was processed (e.g., web, mobile).", "found_categorical_values": ["CARD", "BANK_ACCOUNT", "INTERNAL", "MOBILE_MONEY", "ADMIN", "RFP", "INTERAC", "ACH_BANK_ACCOUNT"]}, {"name": "processorResponseMeta", "data_type": "object", "is_categorical": false, "description": "Metadata returned by the processor regarding the transaction.", "nested_fields": [{"name": "processorResponseMeta.sessionId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "processorResponseMeta.transactionStatusDescription", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}]}, {"name": "rates", "data_type": "object", "is_categorical": false, "description": "Exchange rates applied during the transaction.", "nested_fields": [{"name": "rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "v1TransactionId", "data_type": "string", "is_categorical": false, "description": "Legacy transaction identifier for compatibility."}, {"name": "ledgerTransactionIds", "data_type": "array", "is_categorical": false, "description": "Identifiers for related ledger transactions."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "Timestamp when the transaction was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "Timestamp when the transaction was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "Version key for the document.", "found_categorical_values": ["0", "None"]}, {"name": "sourceAsset", "data_type": "string", "is_categorical": true, "description": "The asset type being sent from the source.", "found_categorical_values": ["USD", "EUR", "NGN", "GBP", "CAD", "UGX", "GHS", "ETB"]}, {"name": "sourceCountry", "data_type": "string", "is_categorical": true, "description": "The country from which the transaction originates.", "found_categorical_values": ["US", "DE", "NG", "GB", "ES", "KE", "CI", "CA", "GH"]}, {"name": "destinationUserId", "data_type": "string", "is_categorical": false, "description": "Identifier for the user receiving the transaction."}, {"name": "destinationAsset", "data_type": "string", "is_categorical": true, "description": "The asset type being received at the destination.", "found_categorical_values": ["NGN", "USD", "GBP", "GHS", "KES", "None", "UGX", "EUR", "XAF", "ETB", "XOF"]}, {"name": "destinationCountry", "data_type": "string", "is_categorical": true, "description": "The country where the transaction is headed.", "found_categorical_values": ["NG", "US", "GB", "GH", "None", "KE", "ES", "UG", "CM", "CA"]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "Current status of the transaction (e.g., pending, completed, failed).", "found_categorical_values": ["SUCCESS", "PENDING", "PROCESSING", "UNKNOWN", "FAILED", "IN_REVIEW"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "Type of transaction (e.g., transfer, payment, refund).", "found_categorical_values": ["WITHDRAW", "DEPOSIT", "TRANSFER", "SWAP", "WELCOME_BONUS"]}, {"name": "sourceAccountDetails", "data_type": "object", "is_categorical": false, "description": "Details of the account sending the funds.", "nested_fields": [{"name": "sourceAccountDetails.name", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "destinationAccountDetails", "data_type": "object", "is_categorical": false, "description": "Details of the account receiving the funds.", "nested_fields": [{"name": "destinationAccountDetails.name", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "channel", "data_type": "string", "is_categorical": true, "description": "The channel through which the transaction was processed (e.g., web, mobile).", "found_categorical_values": ["CARD", "BANK_ACCOUNT", "INTERNAL", "MOBILE_MONEY", "ADMIN", "ACH_BANK_ACCOUNT", "RFP"]}, {"name": "processorResponseMeta", "data_type": "object", "is_categorical": false, "description": "Metadata returned by the processor regarding the transaction.", "nested_fields": [{"name": "processorResponseMeta.description", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "My boy", "test comment", "Request is being processed"]}]}, {"name": "voidTransactionIds", "data_type": "array", "is_categorical": false, "description": "Identifiers for any voided transactions related to this one."}], "sample_documents": "{\n  \"_id\": \"f3a1b2c3d4e5f67890123456\",\n  \"processor\": \"FAKETRANSACTION\",\n  \"processorTransactionId\": \"FakeService-**********abcdef**********-*************\",\n  \"internalTransactionId\": \"FakeService-**********abcdef**********-*************\",\n  \"sourceUserId\": \"abcdef**********abcdef123456\",\n  \"sourceAsset\": \"EUR\",\n  \"sourceAmount\": 5,\n  \"sourceCountry\": \"FR\",\n  \"destinationAsset\": \"JPY\",\n  \"destinationAmount\": 6500.75,\n  \"destinationCountry\": \"JP\",\n  \"status\": \"COMPLETED\",\n  \"type\": \"TRANSFER\",\n  \"destinationAccountDetails\": {\n    \"accountNumber\": \"**********\",\n    \"bankName\": \"Sample Bank\",\n    \"accountName\": \"<PERSON> Doe\",\n    \"phoneNumber\": \"\",\n    \"bankCode\": \"123456\"\n  },\n  \"channel\": \"WALLET\",\n  \"processorResponseMeta\": {\n    \"sessionId\": \"Transfer_Successful\",\n    \"transactionStatusDescription\": \"Transfer_Successful\"\n  },\n  \"rates\": {\n    \"USD\": \"1.1\",\n    \"GBP\": \"0.75\",\n    \"EUR\": \"1\",\n    \"CAD\": \"1.5\",\n    \"NGN\": \"800.00\",\n    \"GHS\": \"15.00\",\n    \"KES\": \"130.00\",\n    \"UGX\": \"4000.00\",\n    \"XAF\": \"600.00\",\n    \"ETB\": \"50.00\",\n    \"HTG\": \"150.00\"\n  },\n  \"v1TransactionId\": \"f3a1b2c3d4e5f67890123457\",\n  \"ledgerTransactionIds\": [\n    \"f3a1b2c3d4e5f67890123458\"\n  ],\n  \"createdAt\": \"2023-04-15T08:30:00.000000\",\n  \"updatedAt\": \"2024-04-22T12:00:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "striperefunds", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "blockeddevices", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "referrals-tiers", "description": "This collection stores the different tiers of referral programs, defining the criteria and rewards associated with each tier. This table contains the following fields: _id, cumulativeThreshold, name, transactionWindow, bonusAmountPerReferree, bonusAmountPerReferrer, isDeactivated, admin, createdAt, updatedAt, __v, referralCode, shouldAutomatePayout, shouldSkipReferrerPayout.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each referral tier."}, {"name": "cumulativeThreshold", "data_type": "integer", "is_categorical": false, "description": "The total amount that must be reached to qualify for this tier."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The name of the referral tier."}, {"name": "transactionWindow", "data_type": "integer", "is_categorical": true, "description": "The time period during which transactions are counted towards the tier.", "found_categorical_values": ["30", "90", "100", "365", "1", "21", "27", "6", "20", "None", "0", "10", "2", "222"]}, {"name": "bonusAmount<PERSON>er<PERSON><PERSON><PERSON><PERSON>", "data_type": "integer", "is_categorical": false, "description": "The bonus amount awarded for each referred user."}, {"name": "bonusAmount<PERSON>er<PERSON><PERSON>errer", "data_type": "integer", "is_categorical": false, "description": "The bonus amount awarded to the referrer for each successful referral."}, {"name": "isDeactivated", "data_type": "integer", "is_categorical": true, "description": "A flag indicating whether the tier is currently active or deactivated.", "found_categorical_values": ["None", "False", "True"]}, {"name": "admin", "data_type": "object", "is_categorical": false, "description": "The identifier of the admin who created or manages this tier.", "nested_fields": [{"name": "admin.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["growth.manager", "engineering.manager", "finance.manager", "growth.member", "finance.member", "compliance.manager", "product.eng.qa.design.manager", "customer.support.manager", "product.eng.qa.design.member"]}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the tier was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the tier was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for managing document revisions.", "found_categorical_values": ["0"]}, {"name": "transactionWindow", "data_type": "integer", "is_categorical": true, "description": "The time period during which transactions are counted towards the tier.", "found_categorical_values": ["30", "90", "100", "365", "1", "6", "20", "2", "27", "None", "222", "21", "0", "10"]}, {"name": "bonusAmount<PERSON>er<PERSON><PERSON>errer", "data_type": "integer", "is_categorical": true, "description": "The bonus amount awarded to the referrer for each successful referral.", "found_categorical_values": ["None", "10", "5", "2", "35", "12", "6", "21", "20", "1", "34"]}, {"name": "referralCode", "data_type": "string", "is_categorical": false, "description": "The unique code associated with this referral tier."}, {"name": "shouldAutomatePayout", "data_type": "integer", "is_categorical": true, "description": "A flag indicating if payouts for this tier should be automated.", "found_categorical_values": ["True", "None", "False"]}, {"name": "shouldSkipReferrerPayout", "data_type": "integer", "is_categorical": true, "description": "A flag indicating if the referrer payout should be skipped under certain conditions.", "found_categorical_values": ["True", "None", "False"]}, {"name": "admin", "data_type": "object", "is_categorical": false, "description": "The identifier of the admin who created or manages this tier.", "nested_fields": [{"name": "admin.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["growth.manager", "engineering.manager", "finance.manager", "finance.member", "growth.member", "customer.support.manager", "product.eng.qa.design.manager", "compliance.manager", "product.eng.qa.design.member"]}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f607g8h9i0j1k2l\",\n  \"cumulativeThreshold\": 200,\n  \"name\": \"Innovative Marketing Team\",\n  \"transactionWindow\": 150,\n  \"bonusAmountPerReferree\": 25,\n  \"bonusAmountPerReferrer\": 20,\n  \"isDeactivated\": true,\n  \"admin\": {\n    \"id\": \"m3n4o5p6q7r8s9t0u1v2w3x4\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"sales.director\"\n  },\n  \"createdAt\": \"2023-12-01T10:15:30.123000\",\n  \"updatedAt\": \"2023-12-01T10:20:45.456000\",\n  \"__v\": 1\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "bankaccounts", "description": "This collection stores information about user bank accounts, including their details and status. This table contains the following fields: _id, user_id, country, currencies, internal_data, deactivated, createdAt, updatedAt, __v. The purpose of this collection is to manage and track user bank account information for financial transactions and account management.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each bank account record."}, {"name": "user_id", "data_type": "string", "is_categorical": false, "description": "The identifier for the user who owns the bank account."}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "The country where the bank account is registered.", "found_categorical_values": ["NG", "None", "US"]}, {"name": "currencies", "data_type": "array", "is_categorical": false, "description": "The currencies supported by the bank account."}, {"name": "internal_data", "data_type": "object", "is_categorical": false, "description": "Additional internal information related to the bank account.", "nested_fields": [{"name": "internal_data.bvn_banking_data", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "internal_data.bvn_banking_data.bvn", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internal_data.bvn_banking_data.provider", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["providus", "None", "vfd"]}, {"name": "internal_data.bvn_banking_data.account_number", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internal_data.bvn_banking_data.createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internal_data.bvn_banking_data.updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "internal_data.createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "internal_data.updatedAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "deactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean indicating whether the bank account is deactivated.", "found_categorical_values": ["False", "None", "True"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the bank account record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the bank account record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used for version control.", "found_categorical_values": ["0", "None"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f6789012345678\",\n  \"user_id\": \"f6g7h8i9j0k1lmnopqrs2tuv\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "rates-batches", "description": "This collection stores information about batches of rates used for financial transactions and arbitrage opportunities. This table contains the following fields: _id, admin, tolerance, ratesLink, arbitrageInfo, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection."}, {"name": "admin", "data_type": "object", "is_categorical": false, "description": "The identifier of the admin user who created or modified the batch.", "nested_fields": [{"name": "admin.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.name", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["<PERSON><PERSON><PERSON><PERSON>", "Us"]}, {"name": "admin.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["finance.manager", "engineering.manager", "operations.manager", "finance.member", "growth.manager"]}]}, {"name": "tolerance", "data_type": "float", "is_categorical": false, "description": "The acceptable range of deviation for the rates in this batch."}, {"name": "ratesLink", "data_type": "string", "is_categorical": false, "description": "A URL or reference link to the source of the rates."}, {"name": "arbitrageInfo", "data_type": "object", "is_categorical": false, "description": "Details regarding any arbitrage opportunities associated with the rates.", "nested_fields": [{"name": "arbitrageInfo.amounts", "data_type": "array", "is_categorical": false, "description": ""}, {"name": "arbitrageInfo.currencies", "data_type": "array", "is_categorical": false, "description": ""}, {"name": "arbitrageInfo.totalAmount", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "arbitrageInfo.profit", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "arbitrageInfo.profitPercentage", "data_type": "float", "is_categorical": false, "description": ""}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the batch was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the batch was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used for version control.", "found_categorical_values": ["0"]}, {"name": "admin", "data_type": "object", "is_categorical": false, "description": "The identifier of the admin user who created or modified the batch.", "nested_fields": [{"name": "admin.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "admin.role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["finance.manager", "engineering.manager", "operations.manager", "finance.member", "growth.manager"]}]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f6789012345678\",\n  \"admin\": {\n    \"id\": \"f1e2d3c4b5a6789012345678\",\n    \"name\": \"<PERSON>\",\n    \"role\": \"marketing.specialist\"\n  },\n  \"tolerance\": 0.45,\n  \"ratesLink\": \"rates-2024-01-15T09:30:00.000Z.csv\",\n  \"arbitrageInfo\": {\n    \"amounts\": [\n      500.123456789,\n      0.********1,\n      0.123456789,\n      0.4567\n    ],\n    \"currencies\": [\n      \"EUR\",\n      \"JPY\",\n      \"CAD\",\n      \"AUD\",\n      \"EUR\"\n    ],\n    \"totalAmount\": 1.567890123456789,\n    \"profit\": 0.567890123456789,\n    \"profitPercentage\": 0.567890123456789\n  },\n  \"createdAt\": \"2024-01-15T09:30:00.000000\",\n  \"updatedAt\": \"2024-01-15T09:30:00.000000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "high-frequency-receivers", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "dynamicratepromousers", "description": "This collection stores information about users who are eligible for dynamic rate promotions. It tracks user participation in promotional campaigns and the details associated with each user entry.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each document in the collection."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier for the user participating in the dynamic rate promotion."}, {"name": "dynamicRatePromoId", "data_type": "string", "is_categorical": false, "description": "The identifier for the specific dynamic rate promotion associated with the user."}, {"name": "created<PERSON>y", "data_type": "string", "is_categorical": false, "description": "The identifier of the user or system that created the record."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"5f6a7b8c9d0e1f234567890a\",\n  \"userId\": \"5f6a7b8c9d0e1f234567890b\",\n  \"dynamicRatePromoId\": \"5f6a7b8c9d0e1f234567890c\",\n  \"createdBy\": \"5f6a7b8c9d0e1f234567890d\",\n  \"createdAt\": \"2023-08-21T10:15:45.123000\",\n  \"updatedAt\": \"2023-08-21T10:15:45.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "rate-logs", "description": "This collection stores logs of currency exchange rates over time. It tracks the conversion rates between different currency symbols and includes metadata about each log entry.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each log entry, automatically generated by MongoDB."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol from which the conversion is made.", "found_categorical_values": ["GBP", "MXN", "PHP", "GHS", "INR", "XOF", "ETB", "USD", "EUR", "UGX", "HTG", "XAF", "NGN", "GNF", "CNY", "KES", "CAD", "EGP", "PKR", "MGA", "ZAR", "MZN", "TZS", "ZMW", "RWF", "MWK"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol to which the conversion is made.", "found_categorical_values": ["PHP", "XAF", "CAD", "EGP", "GBP", "KES", "GHS", "HTG", "ETB", "INR", "USD", "MXN", "EUR", "NGN", "CNY", "UGX", "GNF", "XOF", "ZAR", "RWF", "PKR", "MZN", "TZS", "MGA", "ZMW", "MWK"]}, {"name": "value", "data_type": "string", "is_categorical": false, "description": "The exchange rate value representing the conversion from fromSymbol to toSymbol."}, {"name": "batchId", "data_type": "string", "is_categorical": false, "description": "An identifier for the batch of logs, useful for grouping related entries."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by Mongoose to manage document versioning.", "found_categorical_values": ["0"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the log entry was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the log entry was last updated."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol from which the conversion is made.", "found_categorical_values": ["GHS", "NGN", "XAF", "PHP", "EUR", "CAD", "GBP", "ETB", "HTG", "USD", "CNY", "GNF", "UGX", "INR", "XOF", "KES", "MXN", "EGP", "ZAR", "RWF", "PKR", "MGA", "TZS", "ZMW", "MWK", "MZN"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol to which the conversion is made.", "found_categorical_values": ["GHS", "NGN", "KES", "CAD", "XAF", "GBP", "GNF", "PHP", "INR", "USD", "EGP", "EUR", "MXN", "HTG", "CNY", "XOF", "ETB", "UGX", "PKR", "RWF", "ZAR", "MGA", "MWK", "TZS", "MZN", "ZMW"]}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f607g8h9i0j1k2l\",\n  \"fromSymbol\": \"EUR\",\n  \"toSymbol\": \"GBP\",\n  \"value\": \"42\",\n  \"batchId\": \"m1n2o3p4q5r607s8t9u0v1w2\",\n  \"__v\": 0,\n  \"createdAt\": \"2025-01-15T09:30:45.123000\",\n  \"updatedAt\": \"2025-01-15T09:30:45.123000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "savedcards", "description": "This collection stores information about saved payment cards for users, allowing for easy retrieval and management of card details for transactions.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each document in the collection."}, {"name": "threeDSecure", "data_type": "integer", "is_categorical": true, "description": "Indicates whether the card supports 3D Secure authentication.", "found_categorical_values": ["False", "None"]}, {"name": "paidOut", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if the card has been used for a payout.", "found_categorical_values": ["True", "False", "None"]}, {"name": "deactivated", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if the card has been deactivated by the user.", "found_categorical_values": ["True", "False", "None"]}, {"name": "cardVerified", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if the card has been verified.", "found_categorical_values": ["True", "False", "None"]}, {"name": "default", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if this card is set as the default payment method.", "found_categorical_values": ["False", "None", "True"]}, {"name": "discarded", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if the card has been discarded and is no longer in use.", "found_categorical_values": ["False", "None"]}, {"name": "blocked", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if the card has been blocked for transactions.", "found_categorical_values": ["False", "None"]}, {"name": "cardToken", "data_type": "string", "is_categorical": false, "description": "A token representing the card for secure transactions."}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who owns the card."}, {"name": "cardDetails", "data_type": "object", "is_categorical": false, "description": "An object containing detailed information about the card, such as card number and expiration date.", "nested_fields": [{"name": "cardDetails.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.object", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["card", "None"]}, {"name": "cardDetails.account", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_city", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["us", "ca", "gb", "None"]}, {"name": "cardDetails.address_line1", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_line1_check", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "unchecked", "pass"]}, {"name": "cardDetails.address_line2", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_state", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_zip", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_zip_check", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "unchecked", "pass"]}, {"name": "cardDetails.available_payout_methods", "data_type": "array", "is_categorical": false, "description": ""}, {"name": "cardDetails.brand", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["Visa", "Discover", "MasterCard", "None"]}, {"name": "cardDetails.country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["US", "None", "GB", "CA"]}, {"name": "cardDetails.currency", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["usd", "gbp", "cad", "None"]}, {"name": "cardDetails.cvc_check", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["unchecked", "pass", "None"]}, {"name": "cardDetails.default_for_currency", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None", "False"]}, {"name": "cardDetails.dynamic_last4", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.exp_month", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["8", "9", "2", "11", "12", "1", "5", "7", "None", "3", "6", "4", "10"]}, {"name": "cardDetails.exp_year", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "cardDetails.fingerprint", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "L7zY7QXGcieaBs8z", "UiryH5T5gAtuooOa", "H5tuYWEKYcKVfYKa"]}, {"name": "cardDetails.funding", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["debit", "None", "prepaid", "credit"]}, {"name": "cardDetails.last4", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.tokenization_method", "data_type": "null", "is_categorical": false, "description": ""}]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of card (e.g., Visa, MasterCard).", "found_categorical_values": ["card-charge", "card-payout", "None", "card-true", "card-\"charge\""]}, {"name": "card_location", "data_type": "object", "is_categorical": false, "description": "The location associated with the card, if applicable.", "nested_fields": [{"name": "card_location.lat", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "card_location.lng", "data_type": "float", "is_categorical": false, "description": ""}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the card was added to the collection."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the card details were last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used for versioning.", "found_categorical_values": ["0", "None"]}, {"name": "chargeToken", "data_type": "string", "is_categorical": false, "description": "A token used for processing charges with this card."}, {"name": "payoutToken", "data_type": "string", "is_categorical": false, "description": "A token used for processing payouts with this card."}, {"name": "payoutCardId", "data_type": "string", "is_categorical": false, "description": "The identifier for the card used for payouts."}, {"name": "customerId", "data_type": "string", "is_categorical": false, "description": "The identifier for the customer associated with the card."}, {"name": "cardDetails", "data_type": "object", "is_categorical": false, "description": "An object containing detailed information about the card, such as card number and expiration date.", "nested_fields": [{"name": "cardDetails.id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.object", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["card", "None"]}, {"name": "cardDetails.address_city", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["us", "gb", "ca", "None"]}, {"name": "cardDetails.address_line1", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_line1_check", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "unchecked", "pass"]}, {"name": "cardDetails.address_line2", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_state", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_zip", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.address_zip_check", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "unchecked", "pass"]}, {"name": "cardDetails.brand", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["Visa", "Discover", "MasterCard", "None"]}, {"name": "cardDetails.country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["US", "None", "CA", "GB"]}, {"name": "cardDetails.currency", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["usd", "cad", "gbp", "None"]}, {"name": "cardDetails.cvc_check", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["unchecked", "pass", "None"]}, {"name": "cardDetails.dynamic_last4", "data_type": "null", "is_categorical": false, "description": ""}, {"name": "cardDetails.exp_month", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["8", "9", "2", "12", "11", "1", "5", "7", "None", "6", "3", "4", "10"]}, {"name": "cardDetails.exp_year", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "cardDetails.funding", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["debit", "None", "prepaid", "credit"]}, {"name": "cardDetails.last4", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "cardDetails.tokenization_method", "data_type": "null", "is_categorical": false, "description": ""}]}, {"name": "first6", "data_type": "string", "is_categorical": true, "description": "The first six digits of the card number, used for identification.", "found_categorical_values": ["400005", "601198", "520082", "None", "510510", "555555", "400000"]}, {"name": "verification_charge_id", "data_type": "null", "is_categorical": false, "description": "The identifier for the charge used to verify the card."}, {"name": "verification_charge_amount", "data_type": "null", "is_categorical": false, "description": "The amount charged for the card verification process."}, {"name": "new_card", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if this is a newly added card.", "found_categorical_values": ["True", "None"]}, {"name": "refunded", "data_type": "integer", "is_categorical": true, "description": "A boolean flag indicating if a transaction with this card has been refunded.", "found_categorical_values": ["False", "None"]}], "sample_documents": "{\n  \"_id\": \"62fecdaf29f3c66ea638e9ef\",\n  \"threeDSecure\": true,\n  \"paidOut\": true,\n  \"deactivated\": false,\n  \"cardVerified\": true,\n  \"default\": true,\n  \"discarded\": true,\n  \"blocked\": true,\n  \"cardToken\": \"card_2XYZm4SPhq1wHjj0fWugHFRbG\",\n  \"userId\": \"61fecadec52fef0dea2e2ad3\",\n  \"cardDetails\": {\n    \"id\": \"card_2XYZm4SPhq1wHjj0fWugHFRbG\",\n    \"object\": \"card\",\n    \"account\": \"acct_2JHV53Phq1wHjj0g\",\n    \"address_city\": null,\n    \"address_country\": \"ca\",\n    \"address_line1\": \"45678 Maple Avenue, Toronto, ON, Canada\",\n    \"address_line1_check\": \"fail\",\n    \"address_line2\": null,\n    \"address_state\": null,\n    \"address_zip\": \"90210\",\n    \"address_zip_check\": \"fail\",\n    \"available_payout_methods\": [\n      \"express\",\n      \"standard\"\n    ],\n    \"brand\": \"MasterCard\",\n    \"country\": \"CA\",\n    \"currency\": \"cad\",\n    \"cvc_check\": \"fail\",\n    \"default_for_currency\": true,\n    \"dynamic_last4\": null,\n    \"exp_month\": 12,\n    \"exp_year\": 2025,\n    \"fingerprint\": \"XyZrH5T5gAtuooOb\",\n    \"funding\": \"credit\",\n    \"last4\": \"1234\",\n    \"name\": \"Payout 3\",\n    \"tokenization_method\": null\n  },\n  \"type\": \"card-payout\",\n  \"card_location\": {\n    \"lat\": 45.4215,\n    \"lng\": -75.6972\n  },\n  \"createdAt\": \"2023-02-15T12:30:30.900000\",\n  \"updatedAt\": \"2023-08-10T14:45:45.970000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "rates", "description": "This collection stores exchange rate information between different currency symbols. It is used to track the value of one currency in relation to another over time, allowing for historical analysis and real-time conversion rates.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each document in the collection."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol from which the exchange rate is calculated.", "found_categorical_values": ["GNF", "EUR", "MWK", "ZMW", "ZAR", "PKR", "CAD", "HTG", "TZS", "GBP", "KES", "XAF", "USD", "UGX", "EGP", "GHS", "XOF", "NGN", "INR", "RWF", "MZN", "MGA", "PHP", "CNY", "ETB", "MXN"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol to which the exchange rate is calculated.", "found_categorical_values": ["PKR", "MGA", "MZN", "NGN", "EGP", "KES", "XAF", "MXN", "ETB", "CNY", "PHP", "CAD", "HTG", "RWF", "ZAR", "ZMW", "MWK", "EUR", "UGX", "INR", "GNF", "USD", "XOF", "GHS", "GBP", "TZS"]}, {"name": "value", "data_type": "string", "is_categorical": false, "description": "The numerical value representing the exchange rate from the fromSymbol to the toSymbol."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the exchange rate entry was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the exchange rate entry was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB for internal versioning of documents.", "found_categorical_values": ["0", "None"]}, {"name": "batchId", "data_type": "string", "is_categorical": false, "description": "An identifier for the batch of rates, allowing for grouping of related entries."}, {"name": "fromSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol from which the exchange rate is calculated.", "found_categorical_values": ["MXN", "GNF", "USD", "GBP", "TZS", "HTG", "CAD", "PKR", "ZAR", "ZMW", "MWK", "EUR", "UGX", "XOF", "RWF", "ETB", "GHS", "CNY", "INR", "PHP", "EGP", "MGA", "MZN", "NGN", "XAF", "KES"]}, {"name": "toSymbol", "data_type": "string", "is_categorical": true, "description": "The currency symbol to which the exchange rate is calculated.", "found_categorical_values": ["XAF", "UGX", "ZMW", "MWK", "PKR", "ZAR", "KES", "CAD", "TZS", "HTG", "USD", "GBP", "EGP", "EUR", "GNF", "GHS", "XOF", "MZN", "INR", "NGN", "RWF", "MGA", "PHP", "CNY", "ETB", "MXN"]}, {"name": "userId", "data_type": "string", "is_categorical": false, "description": "The identifier of the user who created or modified the exchange rate entry."}], "sample_documents": "{\n  \"_id\": \"7fda87f3582afe24346ed1ab\",\n  \"fromSymbol\": \"USD\",\n  \"toSymbol\": \"EUR\",\n  \"value\": \"1234.567890\",\n  \"createdAt\": \"2023-03-15T10:20:45.104000\",\n  \"updatedAt\": \"2025-06-20T12:30:55.749000\",\n  \"__v\": 0,\n  \"batchId\": \"12345abcde67890fghijklmno\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "solidcardaccounts", "description": "", "fields": [], "sample_documents": [], "status": "deactivated"}, {"database_name": "dev", "collection_name": "capabilities", "description": "This collection stores information about various capabilities associated with users in the system. Each document represents a specific capability that can be assigned to a user, detailing its requirements and current status.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each capability document, automatically generated by MongoDB."}, {"name": "name", "data_type": "string", "is_categorical": true, "description": "The name of the capability, representing what the capability allows the user to do.", "found_categorical_values": ["ach_withdrawal", "ach_deposit"]}, {"name": "user_id", "data_type": "string", "is_categorical": false, "description": "The identifier of the user to whom this capability is assigned."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "A version key used by Mongoose to track document revisions.", "found_categorical_values": ["0"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the capability was created."}, {"name": "requirements", "data_type": "object", "is_categorical": false, "description": "A list of requirements that must be met for the capability to be granted.", "nested_fields": [{"name": "requirements.kyc", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["succeeded", "missing"]}, {"name": "requirements.updatedAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "requirements.createdAt", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the capability, indicating whether it is active, inactive, or pending.", "found_categorical_values": ["active", "inactive"]}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the capability was last updated."}], "sample_documents": "{\n  \"_id\": \"5f6d1e83ce4907f75eb7b123\",\n  \"name\": \"xyz_withdrawal\",\n  \"user_id\": \"5f6d1db4c87f2f0821782abc\",\n  \"__v\": 0,\n  \"createdAt\": \"2022-09-20T10:30:12.123000\",\n  \"requirements\": {\n    \"kyc\": \"pending\",\n    \"updatedAt\": \"2022-09-20T10:30:25.456000\",\n    \"createdAt\": \"2022-09-20T10:30:25.456000\"\n  },\n  \"status\": \"inactive\",\n  \"updatedAt\": \"2022-09-20T10:30:25.456000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "card_bins", "description": "This collection stores information about card BINs (Bank Identification Numbers) used for payment processing. This table contains the following fields: _id, binNumber, bankNames, isRegulated, createdAt, updatedAt. The purpose of this collection is to maintain a record of BINs along with associated bank information and regulatory status, which is essential for payment gateway operations and fraud prevention.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection, automatically generated by MongoDB."}, {"name": "binNumber", "data_type": "integer", "is_categorical": false, "description": "The Bank Identification Number, which is the first six digits of a credit or debit card used to identify the institution that issued the card."}, {"name": "bankNames", "data_type": "array", "is_categorical": false, "description": "An array of names of banks associated with the BIN, indicating which banks issue cards with this BIN."}, {"name": "isRegulated", "data_type": "integer", "is_categorical": true, "description": "A boolean value indicating whether the BIN is subject to regulatory oversight.", "found_categorical_values": ["True"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the document was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating the last time the document was updated."}], "sample_documents": "{\n  \"_id\": \"7a1b2c3d4e5f67890a1b2c3d\",\n  \"binNumber\": 502345,\n  \"bankNames\": [\n    \"FAKE BANK\"\n  ],\n  \"isRegulated\": false,\n  \"createdAt\": \"2025-10-15T12:30:45.123000\",\n  \"updatedAt\": \"2025-10-15T12:30:45.123000\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "countries", "description": "This collection contains information about various countries, including their identifiers, currency details, and operational capabilities. This table contains the following fields: _id, name, iso2, iso3, currency, providers, idOptions, capabilities, channels, resolvers, status, currencyName, permissions, shouldCaptureAddress, flag, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "Unique identifier for each country record."}, {"name": "name", "data_type": "string", "is_categorical": false, "description": "The official name of the country."}, {"name": "iso2", "data_type": "string", "is_categorical": false, "description": "Two-letter ISO code representing the country."}, {"name": "iso3", "data_type": "string", "is_categorical": false, "description": "Three-letter ISO code representing the country."}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency code used in the country.", "found_categorical_values": ["EUR", "XOF", "XAF", "ETB", "MWK", "RUB", "PKR", "GBP", "TZS", "SLE", "GHS", "RWF", "MGA", "INR", "CNY", "AUD", "ZMW", "MXN", "UYU", "UGX", "CAD", "ZAR", "SSP", "HTG", "USD", "KES", "EGP", "NGN", "MZN", "PHP", "BRL"]}, {"name": "providers", "data_type": "object", "is_categorical": false, "description": "List of service providers available in the country.", "nested_fields": [{"name": "providers.deposit", "data_type": "array", "is_categorical": false, "description": ""}, {"name": "providers.withdrawal", "data_type": "array", "is_categorical": false, "description": ""}, {"name": "providers.kyc", "data_type": "array", "is_categorical": false, "description": ""}]}, {"name": "idOptions", "data_type": "array", "is_categorical": false, "description": "Options for identification methods available in the country.", "array_items": [{"name": "idOptions[].shortName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[].longName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[].id", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[].idType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[].isOptional", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[].isSecondary", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[].validationRegex", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "idOptions[].keyboardType", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "idOptions[]._id", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "capabilities", "data_type": "array", "is_categorical": false, "description": "Operational capabilities specific to the country."}, {"name": "channels", "data_type": "object", "is_categorical": false, "description": "Communication channels available for services in the country.", "nested_fields": [{"name": "channels.deposit", "data_type": "array", "is_categorical": false, "description": ""}, {"name": "channels.withdraw", "data_type": "array", "is_categorical": false, "description": ""}]}, {"name": "resolvers", "data_type": "object", "is_categorical": false, "description": "Entities responsible for resolving issues or queries in the country.", "nested_fields": [{"name": "resolvers.bankAccount", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True"]}, {"name": "resolvers.mobileMoney", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None", "True"]}]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "Current operational status of the country in the database.", "found_categorical_values": ["active", "inactive"]}, {"name": "currencyName", "data_type": "string", "is_categorical": true, "description": "The full name of the currency used in the country.", "found_categorical_values": ["Euro", "West Africa Franc CFA", "Franc CFA", "Pakistani Rupee", "Ugandan <PERSON>", "Egyptian Pound", "Haitian Gourde", "Rwandan <PERSON>", "Mexican Peso", "Indian Rupee", "Ethiopian Birr", "Chinese Yuan", "Philippine Peso", "<PERSON><PERSON>", "Kenyan Shilling", "Mozambican Metical", "Australian Dollar", "South Sudanese Pound", "<PERSON><PERSON>", "Uruguayan Peso", "South African Rand", "Russian Ruble", "Nigerian Naira", "British Pound", "Brazilian Real", "Sierra Leonean Leone", "Canadian Dollar", "Tanzanian <PERSON>", "US Dollar", "Malagasy Ariary", "Zambian <PERSON>"]}, {"name": "permissions", "data_type": "object", "is_categorical": false, "description": "Permissions related to operations in the country.", "nested_fields": [{"name": "permissions.canSwap", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None", "True"]}, {"name": "permissions.canSignup", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "False", "None"]}, {"name": "permissions.canRefer", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "False", "None"]}, {"name": "permissions.canDeposit", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "False", "None"]}, {"name": "permissions.canWithdraw", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "False", "None"]}, {"name": "permissions.canSend", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "None"]}, {"name": "permissions.canScheduleTransaction", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "None"]}, {"name": "permissions._id", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "shouldCaptureAddress", "data_type": "integer", "is_categorical": true, "description": "Indicates whether address capture is required for transactions.", "found_categorical_values": ["True", "None"]}, {"name": "flag", "data_type": "string", "is_categorical": true, "description": "URL or path to the country's flag image.", "found_categorical_values": ["🇬🇧", "🇸🇱", "🇺🇦", "🇫🇮", "🇨🇳", "🇲🇿", "🇲🇱", "🇧🇷", "🇨🇿", "🇨🇲", "🇷🇴", "🇮🇳", "🇧🇯", "🇦🇹", "🇵🇱", "🇺🇸", "🇮🇪", "🇲🇼", "🇦🇺", "🇳🇬", "🇸🇸", "🇨🇾", "🇧🇪", "🇪🇪", "🇸🇰", "🇸🇪", "🇹🇿", "🇧🇬", "🇱🇺", "🇪🇬", "🇳🇴", "🇷🇼", "🇱🇹", "🇭🇷", "🇿🇲", "🇲🇽", "🇲🇬", "🇭🇹", "🇩🇰", "🇬🇷", "🇵🇰", "🇸🇮", "🇳🇱", "🇲🇹", "🇩🇪", "🇬🇳", "🇱🇻", "🇨🇦", "🇪🇹", "🇵🇭"]}, {"name": "iso2", "data_type": "string", "is_categorical": true, "description": "Two-letter ISO code representing the country.", "found_categorical_values": ["CY", "BG", "LU", "NL", "FR", "SN", "UY", "SI", "CG", "SS", "EG", "FI", "RU", "CI", "GB", "BF", "CN", "BR", "SL", "TZ", "MX", "GN", "SE", "SK", "ZA", "PH", "GR", "AT", "IE", "GH", "EE", "DE", "US", "PL", "LV", "ES", "ML", "MT", "NG", "PK", "BE", "ET", "IN", "UA", "BJ", "KE", "ZM", "HR", "RO", "CA"]}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "Version key for internal use in MongoDB.", "found_categorical_values": ["0", "None"]}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency code used in the country.", "found_categorical_values": ["EUR", "XOF", "XAF", "MXN", "ETB", "INR", "CNY", "RWF", "GHS", "MGA", "SLE", "TZS", "GBP", "PKR", "RUB", "MWK", "BRL", "ZMW", "PHP", "MZN", "KES", "EGP", "NGN", "USD", "SSP", "HTG", "ZAR", "AUD", "CAD", "UYU", "UGX"]}, {"name": "currencyName", "data_type": "string", "is_categorical": true, "description": "The full name of the currency used in the country.", "found_categorical_values": ["Euro", "West Africa Franc CFA", "Franc CFA", "Egyptian Pound", "Nigerian Naira", "Sierra Leonean Leone", "Brazilian Real", "British Pound", "Canadian Dollar", "US Dollar", "Malagasy Ariary", "Tanzanian <PERSON>", "Zambian <PERSON>", "Australian Dollar", "Haitian Gourde", "Ugandan <PERSON>", "Rwandan <PERSON>", "South African Rand", "Indian Rupee", "Mexican Peso", "Philippine Peso", "Ethiopian Birr", "Mozambican Metical", "<PERSON><PERSON>", "Pakistani Rupee", "Kenyan Shilling", "<PERSON><PERSON>", "Chinese Yuan", "South Sudanese Pound", "Uruguayan Peso", "Russian Ruble"]}, {"name": "iso3", "data_type": "string", "is_categorical": true, "description": "Three-letter ISO code representing the country.", "found_categorical_values": ["LTU", "USA", "NOR", "ITA", "ESP", "MOZ", "GBR", "BEL", "SLE", "LVA", "ZMB", "GIN", "HUN", "IRL", "CZE", "PAK", "HRV", "HTI", "DNK", "TGO", "MLT", "CAN", "CMR", "MDG", "BFA", "RUS", "EST", "EGY", "MLI", "MEX", "PHL", "CYP", "SSD", "FRA", "SVN", "ROU", "TZA", "GHA", "SEN", "POL", "UKR", "RWA", "BRA", "IND", "CHN", "BGR", "PRT", "FIN", "BEN", "COG"]}, {"name": "flag", "data_type": "string", "is_categorical": false, "description": "URL or path to the country's flag image."}], "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"name\": \"Randomland\",\n  \"iso2\": \"RL\",\n  \"iso3\": \"RND\",\n  \"currency\": \"RLD\",\n  \"providers\": {\n    \"deposit\": [],\n    \"withdrawal\": [],\n    \"kyc\": []\n  },\n  \"idOptions\": [\n    {\n      \"shortName\": \"Identity Card\",\n      \"longName\": \"Identity Card\",\n      \"id\": \"identity_card\",\n      \"idType\": \"identity_card\",\n      \"isOptional\": false,\n      \"isSecondary\": false,\n      \"validationRegex\": \"^[a-zA-Z0-9]{9}$\",\n      \"keyboardType\": \"default\",\n      \"_id\": \"abcdef**********abcdef01\"\n    },\n    {\n      \"shortName\": \"Travel Document\",\n      \"longName\": \"Travel Document\",\n      \"id\": \"travel_document\",\n      \"idType\": \"travel_document\",\n      \"isOptional\": false,\n      \"isSecondary\": false,\n      \"validationRegex\": \"^[a-zA-Z0-9]{9}$\",\n      \"keyboardType\": \"default\",\n      \"_id\": \"abcdef**********abcdef02\"\n    },\n    {\n      \"shortName\": \"Vehicle Registration\",\n      \"longName\": \"Vehicle Registration\",\n      \"id\": \"vehicle_registration\",\n      \"idType\": \"vehicle_registration\",\n      \"isOptional\": false,\n      \"isSecondary\": false,\n      \"validationRegex\": \"^[a-zA-Z0-9]{7,15}$\",\n      \"keyboardType\": \"default\",\n      \"_id\": \"abcdef**********abcdef03\"\n    },\n    {\n      \"shortName\": \"Work Permit\",\n      \"longName\": \"Work Permit\",\n      \"id\": \"work_permit\",\n      \"idType\": \"work_permit\",\n      \"isOptional\": false,\n      \"isSecondary\": false,\n      \"validationRegex\": \"^[a-zA-Z0-9]{9}$\",\n      \"keyboardType\": \"default\",\n      \"_id\": \"abcdef**********abcdef04\"\n    }\n  ],\n  \"capabilities\": [\n    \"DEPOSIT\",\n    \"WITHDRAW\"\n  ],\n  \"channels\": {\n    \"deposit\": [\n      \"BANK\"\n    ],\n    \"withdraw\": []\n  },\n  \"resolvers\": {\n    \"bankAccount\": false,\n    \"mobileMoney\": false\n  },\n  \"status\": \"inactive\",\n  \"currencyName\": \"Random Dollar\",\n  \"permissions\": {\n    \"canSwap\": false,\n    \"canSignup\": true,\n    \"canRefer\": true,\n    \"canDeposit\": true,\n    \"canWithdraw\": false,\n    \"canSend\": true,\n    \"canScheduleTransaction\": true,\n    \"_id\": \"abcdef**********abcdef05\"\n  },\n  \"shouldCaptureAddress\": true,\n  \"flag\": \"\\ud83c\\uddf7\\ud83c\\uddf4\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "pendingpayouts", "description": "This collection stores information about payouts that are pending approval or processing. It tracks the details of each payout request, including the associated account, the amount to be paid, and relevant timestamps.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "The unique identifier for each payout record."}, {"name": "accountId", "data_type": "string", "is_categorical": false, "description": "The identifier of the account associated with the payout."}, {"name": "amount", "data_type": "string", "is_categorical": true, "description": "The total amount of money to be paid out.", "found_categorical_values": ["2", "10", "20"]}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the payout amount is specified.", "found_categorical_values": ["USD"]}, {"name": "reference", "data_type": "string", "is_categorical": false, "description": "A reference number or note associated with the payout."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the payout record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp indicating when the payout record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key used by MongoDB to manage document revisions.", "found_categorical_values": ["0"]}, {"name": "amount", "data_type": "string", "is_categorical": true, "description": "The total amount of money to be paid out.", "found_categorical_values": ["2", "20", "10"]}], "sample_documents": "{\n  \"_id\": \"7f3b2c1a4e5d6f8a9b0c1d2e\",\n  \"accountId\": \"acct_2XY3opGH7j5yzkLm\",\n  \"amount\": \"5\",\n  \"currency\": \"EUR\",\n  \"reference\": \"tr_2PQwg4C9lzjH12f6J7PGmO8R\",\n  \"createdAt\": \"2023-02-20T12:45:30.123000\",\n  \"updatedAt\": \"2023-02-20T12:45:30.123000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "apprates", "description": "This collection stores the application rates for various services or products, allowing for tracking and analysis of rate changes over time. This table contains the following fields: _id, updatedAt, createdAt, dynamic_rates, __v. The purpose of this collection is to maintain a historical record of application rates, enabling users to retrieve and analyze rate trends and changes.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each document in the collection, automatically generated by MongoDB."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the document was last updated."}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "A timestamp indicating when the document was created."}, {"name": "dynamic_rates", "data_type": "object", "is_categorical": false, "description": "An array containing the various application rates that can change over time.", "nested_fields": [{"name": "dynamic_rates.USD", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.USD.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.USD.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.USD.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.GBP", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.GBP.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.GBP.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GBP.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.EUR", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.EUR.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.EUR.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.EUR.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.CAD", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.CAD.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.CAD.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.CAD.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.NGN", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.NGN.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.NGN.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.NGN.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.GHS", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.GHS.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.GHS.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.GHS.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.KES", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.KES.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.KES.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.KES", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["1"]}, {"name": "dynamic_rates.KES.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.KES.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.UGX", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.UGX.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.UGX.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.UGX.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.XAF", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.XAF.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.XAF.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.XAF", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["1"]}, {"name": "dynamic_rates.XAF.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.XAF.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.ETB", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.ETB.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.ETB.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.ETB.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}, {"name": "dynamic_rates.HTG", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.HTG.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "dynamic_rates.HTG.rates.USD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.GBP", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.EUR", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.CAD", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.NGN", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.GHS", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.KES", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.UGX", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.XAF", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.ETB", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "dynamic_rates.HTG.rates.HTG", "data_type": "string", "is_categorical": false, "description": ""}]}]}]}, {"name": "__v", "data_type": "integer", "is_categorical": false, "description": "A version key used by Mongoose to track document revisions."}], "sample_documents": "{\n  \"_id\": \"5f4e3d2c1b8a4e6f8c9b1a2b\",\n  \"updatedAt\": \"2023-05-15T14:22:00.059000\",\n  \"createdAt\": \"2021-06-20T11:15:00.510000\",\n  \"dynamic_rates\": {\n    \"USD\": {\n      \"rates\": {\n        \"USD\": \"1\",\n        \"GBP\": \"0.75432\",\n        \"EUR\": \"0.8456\",\n        \"CAD\": \"1.19876\",\n        \"NGN\": \"680.45\",\n        \"GHS\": \"11.3456\",\n        \"KES\": \"118.456\",\n        \"UGX\": \"3456.78\",\n        \"XAF\": \"567.123\",\n        \"ETB\": \"49.8765\",\n        \"HTG\": \"140.456\"\n      }\n    },\n    \"GBP\": {\n      \"rates\": {\n        \"USD\": \"1.3254\",\n        \"GBP\": \"1\",\n        \"EUR\": \"1.09876543\",\n        \"CAD\": \"1.487654321\",\n        \"NGN\": \"845.123456\",\n        \"GHS\": \"14.5678901\",\n        \"KES\": \"145.6789012\",\n        \"UGX\": \"4321.987654\",\n        \"XAF\": \"678.1234567\",\n        \"ETB\": \"60.12345678\",\n        \"HTG\": \"175.1234567\"\n      }\n    },\n    \"EUR\": {\n      \"rates\": {\n        \"USD\": \"1.12\",\n        \"GBP\": \"0.87654321\",\n        \"EUR\": \"1\",\n        \"CAD\": \"1.345678901\",\n        \"NGN\": \"700.123456\",\n        \"GHS\": \"11.23456\",\n        \"KES\": \"130.45678\",\n        \"UGX\": \"4000.1234\",\n        \"XAF\": \"600.12345\",\n        \"ETB\": \"55.123456\",\n        \"HTG\": \"150.12345\"\n      }\n    },\n    \"CAD\": {\n      \"rates\": {\n        \"USD\": \"0.765\",\n        \"GBP\": \"0.6123456789\",\n        \"EUR\": \"0.7123456789\",\n        \"CAD\": \"1\",\n        \"NGN\": \"500.123456\",\n        \"GHS\": \"9.8765432\",\n        \"KES\": \"90.12345\",\n        \"UGX\": \"2500.1234\",\n        \"XAF\": \"400.12345\",\n        \"ETB\": \"37.123456\",\n        \"HTG\": \"105.123456\"\n      }\n    },\n    \"NGN\": {\n      \"rates\": {\n        \"USD\": \"0.00123456789\",\n        \"GBP\": \"0.00101234567\",\n        \"EUR\": \"0.00112345678\",\n        \"CAD\": \"0.00156789\",\n        \"NGN\": \"1\",\n        \"GHS\": \"0.0145678901\",\n        \"KES\": \"0.1581234567\",\n        \"UGX\": \"4.567890123\",\n        \"XAF\": \"0.7654321\",\n        \"ETB\": \"0.0678901234\",\n        \"HTG\": \"0.1890123456\"\n      }\n    },\n    \"GHS\": {\n      \"rates\": {\n        \"USD\": \"0.0654321\",\n        \"GBP\": \"0.0523456789\",\n        \"EUR\": \"0.0589012345\",\n        \"CAD\": \"0.0834567890\",\n        \"NGN\": \"45.67890123\",\n        \"GHS\": \"1\",\n        \"KES\": \"8.123456789\",\n        \"UGX\": \"230.1234567\",\n        \"XAF\": \"38.12345678\",\n        \"ETB\": \"3.456789012\",\n        \"HTG\": \"9.123456789\"\n      }\n    },\n    \"KES\": {\n      \"rates\": {\n        \"USD\": \"0.007123456789\",\n        \"GBP\": \"0.005678901234\",\n        \"EUR\": \"0.006123456789\",\n        \"CAD\": \"0.009123456789\",\n        \"NGN\": \"5.123456789\",\n        \"GHS\": \"0.0901234567\",\n        \"KES\": \"1\",\n        \"UGX\": \"26.12345678\",\n        \"XAF\": \"4.123456789\",\n        \"ETB\": \"0.4012345678\",\n        \"HTG\": \"1.123456789\"\n      }\n    },\n    \"UGX\": {\n      \"rates\": {\n        \"USD\": \"0.0002456789\",\n        \"GBP\": \"0.0001********1\",\n        \"EUR\": \"0.0002234567890\",\n        \"CAD\": \"0.0003212345678\",\n        \"NGN\": \"0.1801234567\",\n        \"GHS\": \"0.0023456789\",\n        \"KES\": \"0.0301234567\",\n        \"UGX\": \"1\",\n        \"XAF\": \"0.1456789012\",\n        \"ETB\": \"0.0123456789\",\n        \"HTG\": \"0.0361234567\"\n      }\n    },\n    \"XAF\": {\n      \"rates\": {\n        \"USD\": \"0.0015\",\n        \"GBP\": \"0.0012345678\",\n        \"EUR\": \"0.0014567890\",\n        \"CAD\": \"0.0021234567\",\n        \"NGN\": \"1.123456\",\n        \"GHS\": \"0.01********\",\n        \"KES\": \"0.2012345678\",\n        \"UGX\": \"5.123456789\",\n        \"XAF\": \"1\",\n        \"ETB\": \"0.0876543210\",\n        \"HTG\": \"0.2456789012\"\n      }\n    },\n    \"ETB\": {\n      \"rates\": {\n        \"USD\": \"0.018\",\n        \"GBP\": \"0.0145678901\",\n        \"EUR\": \"0.0167890123\",\n        \"CAD\": \"0.0234567890\",\n        \"NGN\": \"12.3456789\",\n        \"GHS\": \"0.2201234567\",\n        \"KES\": \"2.345678901\",\n        \"UGX\": \"67.12345678\",\n        \"XAF\": \"10.12345678\",\n        \"ETB\": \"1\",\n        \"HTG\": \"2.678901234\"\n      }\n    },\n    \"HTG\": {\n      \"rates\": {\n        \"USD\": \"0.0075\",\n        \"GBP\": \"0.0051234567\",\n        \"EUR\": \"0.0056789012\",\n        \"CAD\": \"0.0081234567\",\n        \"NGN\": \"4.567890123\",\n        \"GHS\": \"0.0791234567\",\n        \"KES\": \"0.8001234567\",\n        \"UGX\": \"23.45678901\",\n        \"XAF\": \"3.456789012\",\n        \"ETB\": \"0.3501234567\",\n        \"HTG\": \"1\"\n      }\n    }\n  },\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "users", "description": "", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "email", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "password", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "userName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "displayName", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "isAdmin", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True"]}, {"name": "photo", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "https://lh6.googleusercontent.com/-V7lSGoZNR8s/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuckJlPTDY9x4n_KR0WVQyl6eHx45OQ/photo.jpg"]}, {"name": "googleId", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "appleId", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "facebookId", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "firstName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "lastName", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "gender", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "about", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "pin", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "securityEnabled", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "defaultPin", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True"]}, {"name": "bvn_phone_number", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "bvnVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "bitcoin_wallet_address", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "ethereum_wallet_address", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "stellar_account_address", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "phone", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "whatsapp_no", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "phoneVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["ng", "us", "ke", "ca", "gb", "gh", "ug", "fr", "rw", "cm", "ci", "za", "et", "de", "be", "es", "ee", "sn", "tz", "cy", "zm", "pl"]}, {"name": "socialProfileLink", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "new_wallet_name", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True", "None"]}, {"name": "us_account_id", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "operations.manager"]}, {"name": "roles", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None"]}, {"name": "usd_limit", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "ngn_limit", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["1200000", "**********", "120000"]}, {"name": "per_transaction_limit", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "per_daily_limit", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "docVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True"]}, {"name": "isVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "verifyToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "verifyShortToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "verifyExpires", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "resetToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "resetShortToken", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "paystack_recipient_code", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "RCP_sh93rf76nizjmsc"]}, {"name": "referrer", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "tests550", "mamafavour", "None", "$alexy448", "greg", "fabiansandra3$", "walte464", "oza1stborn", "yomia796", "TundeEnd", "cardk324", "funmilay1993#", "sea", "jade@4008", "imusa78", "ecfe1156", "kenya752", "234", "bolao783", "fasal256", "internal2022@@", "EZRAG162", "tolu_influence", "iuuu", "nexxw84", "omoni386", "virtu120", "larteypatrick5118", "Omoni386", "********", "stage521"]}, {"name": "active_referrer", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None"]}, {"name": "referral_bonus_fulfilled", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None"]}, {"name": "num_of_referrals", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "None"]}, {"name": "securityNotes", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "User multple Device Login", "None"]}, {"name": "has_rated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "None", "True"]}, {"name": "dailyskips", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "None"]}, {"name": "agent_withdrawal_count", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "None"]}, {"name": "no_of_transactions_for_rating", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "no_of_transactions_withdrawal_frequency", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "7", "10", "5", "None"]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0"]}, {"name": "isAdmin", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "photo", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "https://lh3.googleusercontent.com/a-/AOh14GgiIpUwhfA85uzkFBrd-cGtcDPzt5w92Aitg_k9", "None"]}, {"name": "bvn_phone_number", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "bvnVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True"]}, {"name": "phoneVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True"]}, {"name": "country", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["ng", "us", "gb", "ke", "ug", "gh", "ca", "fr", "cm", "za", "de", "rw", "sn", "et", "ci", "eg", "be", "es", "tz", "it", "zm", "cy"]}, {"name": "new_wallet_name", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["True"]}, {"name": "role", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "super_admin", "None", "engineering.manager"]}, {"name": "roles", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "None", "super_admin"]}, {"name": "ngn_limit", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["1200000", "400000", "120000", "**********"]}, {"name": "docVerified", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "paystack_recipient_code", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": [""]}, {"name": "referrer", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "tests550", "mamafavour", "tolu_influence", "b<PERSON><PERSON><PERSON><PERSON>", "Stage521", "isaac", "prayp346", "fifif", "wewewewe", "<PERSON><PERSON><PERSON><PERSON><PERSON>sfx4504", "234", "sefl", "ecfe1156", "$oduwolee1123", "iuuu", "ewwee", "b35k78", "bolao783", "essien2000$", "harrison19", "STAGE521", "test", "testc610", "Iran0191"]}, {"name": "active_referrer", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False"]}, {"name": "securityNotes", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["", "User multple Device Login"]}, {"name": "has_rated", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["False", "True", "None"]}, {"name": "dailyskips", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0"]}, {"name": "agent_withdrawal_count", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "1"]}, {"name": "no_of_transactions_withdrawal_frequency", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["0", "7", "10", "5", "None", "15"]}], "sample_documents": "{\n  \"_id\": \"5f6a7d8c9e0f1a2b3c4d5e6f\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"$2a$10$randomhashedpassword**********abcdefg\",\n  \"userName\": \"user1234\",\n  \"displayName\": \"\",\n  \"isAdmin\": false,\n  \"photo\": \"\",\n  \"googleId\": \"\",\n  \"appleId\": \"\",\n  \"facebookId\": \"\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"gender\": \"\",\n  \"about\": \"\",\n  \"pin\": \"\",\n  \"securityEnabled\": true,\n  \"defaultPin\": true,\n  \"bvn_phone_number\": \"\",\n  \"bvnVerified\": false,\n  \"bitcoin_wallet_address\": \"\",\n  \"ethereum_wallet_address\": \"\",\n  \"stellar_account_address\": \"\",\n  \"phone\": \"+*************\",\n  \"whatsapp_no\": \"\",\n  \"phoneVerified\": false,\n  \"country\": \"us\",\n  \"socialProfileLink\": \"\",\n  \"new_wallet_name\": true,\n  \"us_account_id\": \"\",\n  \"role\": \"\",\n  \"roles\": \"\",\n  \"usd_limit\": 6000,\n  \"ngn_limit\": 1500000,\n  \"per_transaction_limit\": 1200,\n  \"per_daily_limit\": 6000,\n  \"docVerified\": false,\n  \"isVerified\": true,\n  \"verifyToken\": \"randomveriftoken**********abcdefg\",\n  \"verifyShortToken\": \"123456\",\n  \"verifyExpires\": \"2023-08-02T16:11:45.695000\",\n  \"resetToken\": \"\",\n  \"resetShortToken\": \"\",\n  \"paystack_recipient_code\": \"\",\n  \"referrer\": \"\",\n  \"active_referrer\": false,\n  \"referral_bonus_fulfilled\": false,\n  \"num_of_referrals\": 0,\n  \"securityNotes\": \"\",\n  \"has_rated\": false,\n  \"dailyskips\": 0,\n  \"agent_withdrawal_count\": 0,\n  \"no_of_transactions_for_rating\": 0,\n  \"no_of_transactions_withdrawal_frequency\": 5,\n  \"createdAt\": \"2023-07-27T16:11:45.704000\",\n  \"updatedAt\": \"2023-07-27T16:11:45.809000\",\n  \"__v\": 0\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "kycs", "description": "This collection stores Know Your Customer (KYC) information for users, which is essential for identity verification and compliance with regulatory requirements. This table contains the following fields: _id, userID, id_number, selfiePhoto, imageUrlFront, imageUrlBack, idType, address, dateOfBirth, status, provider, successAcked, timeline, createdAt, updatedAt, __v, attempts, details.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "A unique identifier for each KYC record."}, {"name": "userID", "data_type": "string", "is_categorical": false, "description": "The unique identifier of the user associated with the KYC record."}, {"name": "id_number", "data_type": "string", "is_categorical": false, "description": "The identification number provided by the user, such as a national ID or passport number."}, {"name": "selfie<PERSON><PERSON><PERSON>", "data_type": "string", "is_categorical": false, "description": "A photo of the user taken for identity verification."}, {"name": "imageUrlFront", "data_type": "string", "is_categorical": false, "description": "The URL of the front side image of the user's identification document."}, {"name": "imageUrlBack", "data_type": "string", "is_categorical": false, "description": "The URL of the back side image of the user's identification document."}, {"name": "idType", "data_type": "string", "is_categorical": true, "description": "The type of identification document provided by the user (e.g., passport, driver's license).", "found_categorical_values": ["passport", "drivers_license", "national_id", "driverLicense", "Residence_permit", "ALIEN_CARD", "None", "BVN", "NIN", "VOTER_ID", "Passport", "NATIONAL_ID", "NATIONAL_ID_NO_PHOTO"]}, {"name": "address", "data_type": "string", "is_categorical": false, "description": "The residential address of the user."}, {"name": "dateOfBirth", "data_type": "object", "is_categorical": false, "description": "The date of birth of the user.", "nested_fields": [{"name": "dateOfBirth.year", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "dateOfBirth.month", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["2", "None", "07", "06", "05", "03", "04", "12", "01", "02", "08", "12", "09", "11", "10", "8", "7", "9", "1"]}, {"name": "dateOfBirth.day", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None", "22", "01", "07", "02", "10", "22", "08", "29", "04", "05", "10", "14", "06", "2", "11", "13", "21", "26", "12", "4", "1", "27", "13", "7", "17", "30", "25", "16", "03", "15", "25", "9", "19"]}]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the KYC process (e.g., pending, approved, rejected).", "found_categorical_values": ["pending", "verified", "unverified", "None", "unsubmitted", "failed"]}, {"name": "provider", "data_type": "string", "is_categorical": true, "description": "The service provider handling the KYC verification.", "found_categorical_values": ["providus", "veriff", "smile", "stripe", "None", "admin"]}, {"name": "successAcked", "data_type": "integer", "is_categorical": true, "description": "A flag indicating whether the KYC process was successfully acknowledged.", "found_categorical_values": ["False", "None", "True"]}, {"name": "timeline", "data_type": "array", "is_categorical": false, "description": "A record of the timestamps for various stages of the KYC process.", "array_items": [{"name": "timeline[].id", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "timeline[].timestamp", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "timeline[].state", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None"]}, {"name": "timeline[].description", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the KYC record was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "The timestamp when the KYC record was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "The version key for the document, used for version control.", "found_categorical_values": ["0", "None"]}, {"name": "attempts", "data_type": "integer", "is_categorical": true, "description": "The number of attempts made to verify the user's identity.", "found_categorical_values": ["None", "1", "3", "2", "4", "6", "5"]}, {"name": "details", "data_type": "string", "is_categorical": false, "description": "Additional details or notes regarding the KYC process."}, {"name": "idType", "data_type": "string", "is_categorical": true, "description": "The type of identification document provided by the user (e.g., passport, driver's license).", "found_categorical_values": ["passport", "drivers_license", "national_id", "driverLicense", "Residence_permit", "ALIEN_CARD", "None", "BVN", "NIN", "VOTER_ID", "NATIONAL_ID_NO_PHOTO", "Passport", "NATIONAL_ID", "Driver License", "ssnit", "new_voter_id"]}, {"name": "dateOfBirth", "data_type": "object", "is_categorical": false, "description": "The date of birth of the user.", "nested_fields": [{"name": "dateOfBirth.year", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "dateOfBirth.month", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["2", "None", "07", "05", "06", "04", "03", "08", "12", "02", "01", "09", "12", "8", "11", "7"]}, {"name": "dateOfBirth.day", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["22", "None", "01", "07", "22", "02", "29", "04", "10", "08", "19", "2", "10", "05", "11", "14", "26", "21", "13", "12", "13", "06", "7", "30", "25", "16", "03", "9", "25"]}]}, {"name": "status", "data_type": "string", "is_categorical": true, "description": "The current status of the KYC process (e.g., pending, approved, rejected).", "found_categorical_values": ["pending", "verified", "unverified", "None", "failed"]}], "sample_documents": "{\n  \"_id\": \"7f3e1c2b4a5d6e7f8a9b0c1d\",\n  \"userID\": \"7f3e1c2b4a5d6e7f8a9b0c2e\",\n  \"id_number\": \"48273645\",\n  \"selfiePhoto\": \"selfie_jane.jpg\",\n  \"imageUrlFront\": \"selfie_jane.jpg\",\n  \"imageUrlBack\": \"selfie_jane.jpg\",\n  \"idType\": \"driver_license\",\n  \"address\": \"1234 Maple Avenue\",\n  \"dateOfBirth\": {\n    \"year\": 1985,\n    \"month\": 11,\n    \"day\": 5\n  },\n  \"status\": \"verified\",\n  \"provider\": \"fintech\",\n  \"successAcked\": true,\n  \"timeline\": [\n    {\n      \"id\": \"cd7b1234-56ef-7890-abcd-ef**********\",\n      \"timestamp\": \"2023-03-15T14:30:00.000Z\",\n      \"state\": \"completed\",\n      \"description\": \"KYC completed\"\n    }\n  ],\n  \"createdAt\": \"2023-03-15T14:30:00.000000\",\n  \"updatedAt\": \"2023-03-15T14:30:00.000000\",\n  \"__v\": 1,\n  \"attempts\": 2,\n  \"details\": \"Valid IdType\"\n}", "status": "deactivated"}, {"database_name": "dev", "collection_name": "alltransactions", "description": "This collection stores all transaction records related to cryptocurrency exchanges and transfers. It captures detailed information about each transaction, including amounts, types, and associated user data. This table contains the following fields: _id, amountCrypto, amount, currency, type, fulfillmentAsset, fulfillmentAssetValue, payoutId, rate, isChargeWithdrawal, processor, user, ledgerTransactionId, reference, state, bank_account_info, customer_details, sourceCountry, type_of_transaction, rates, createdAt, updatedAt, __v.", "fields": [{"name": "_id", "data_type": "string", "is_categorical": false, "description": "Unique identifier for each transaction record."}, {"name": "amountCrypto", "data_type": "string", "is_categorical": false, "description": "The amount of cryptocurrency involved in the transaction."}, {"name": "amount", "data_type": "string", "is_categorical": false, "description": "The total amount of the transaction in fiat currency."}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the transaction amount is denominated.", "found_categorical_values": ["USD", "NGN", "GBP", "CAD", "KES", "GHS", "UGX", "BXC", "ngn", "BTC", "usd"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of transaction (e.g., deposit, withdrawal, transfer).", "found_categorical_values": ["None", "Withdraw", "fiat-fiat-swap", "sell", "withdraw", "payout-to-card", "fiat-crypto-swap", "fiat-managed-crypto-swap", "payout to card"]}, {"name": "fulfillmentAsset", "data_type": "string", "is_categorical": true, "description": "The asset used to fulfill the transaction.", "found_categorical_values": ["NGN", "None", "USD", "CAD", "GBP", "GHS", "KES", "UGX", "btc"]}, {"name": "fulfillmentAssetValue", "data_type": "string", "is_categorical": false, "description": "The value of the fulfillment asset in the transaction."}, {"name": "payoutId", "data_type": "string", "is_categorical": false, "description": "Identifier for the payout associated with the transaction."}, {"name": "rate", "data_type": "string", "is_categorical": true, "description": "Exchange rate applied to the transaction.", "found_categorical_values": ["None", "0"]}, {"name": "is<PERSON><PERSON><PERSON>", "data_type": "integer", "is_categorical": true, "description": "Indicates if the transaction is a charge withdrawal.", "found_categorical_values": ["None", "True", "False"]}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor handling the transaction.", "found_categorical_values": ["None", "STRIPE", "vfd", "stripe", "providus", "PROVIDUS", "zeepay", "beyonic", "mono", "test_faucet", "binance", "cellulant", "mobile", "VFD"]}, {"name": "user", "data_type": "string", "is_categorical": false, "description": "Identifier for the user associated with the transaction."}, {"name": "ledgerTransactionId", "data_type": "string", "is_categorical": false, "description": "Identifier for the transaction in the ledger."}, {"name": "reference", "data_type": "string", "is_categorical": false, "description": "Reference number or note associated with the transaction."}, {"name": "state", "data_type": "string", "is_categorical": true, "description": "Current state of the transaction (e.g., pending, completed, failed).", "found_categorical_values": ["None", "SUCCESS", "Successful", "pending", "REFUNDED", "Pending", "Failed", "Refunded", "Processing", "PENDING", "failed", "RETRY", "Completed", "PROCESSING", "Cancelled", "succeeded", "Canceled"]}, {"name": "bank_account_info", "data_type": "object", "is_categorical": false, "description": "Information about the bank account involved in the transaction.", "nested_fields": [{"name": "bank_account_info.account_number", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "bank_account_info.bank_name", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "Access Bank", "Zenith Bank", "Cellulant", "Access Bank (Diamond)", "Citi Bank", "ECOBANK", "ABBEY MORTGAGE BANK", "FCMB", "Fidelity Bank", "First City Monument Bank", "AccessMobile", "GTBank", "AB Microfinance bank", "Enterprise Bank", "JAIZ Bank", "OPAY", "Sterling Bank", "First Bank", "Above Only Microfinance bank"]}, {"name": "bank_account_info.account_name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "bank_account_info.phoneNumber", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "bank_account_info.bank_code", "data_type": "string", "is_categorical": true, "description": "", "found_categorical_values": ["None", "000014", "000005", "000015", "000009", "100005", "000010", "000003", "070010", "000007", "000006", "000019", "100004", "100013", "090270", "000001", "030"]}]}, {"name": "customer_details", "data_type": "object", "is_categorical": false, "description": "Details about the customer making the transaction.", "nested_fields": [{"name": "customer_details.name", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "customer_details.email", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "customer_details.username", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "customer_details.photo", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "customer_details.phone", "data_type": "string", "is_categorical": false, "description": ""}]}, {"name": "sourceCountry", "data_type": "string", "is_categorical": true, "description": "Country from which the transaction originated.", "found_categorical_values": ["US", "NG", "us", "CA", "GB", "ng", "GH", "None", "KE", "UG", "ca", "ke", "ug"]}, {"name": "type_of_transaction", "data_type": "string", "is_categorical": true, "description": "Specific type of transaction (e.g., buy, sell, exchange).", "found_categorical_values": ["sell-transactions", "card-charge", "transactions", "admin_credit", "swap", "transfer-fiat", "reversal", "bulk-payment", "agent-withdrawal", "banking-deposit", "agent-deposit", "swapping", "Deposit To Wallet", "admin_debit", "bank_transfer"]}, {"name": "rates", "data_type": "object", "is_categorical": false, "description": "Historical rates associated with the transaction.", "nested_fields": [{"name": "rates.CAD", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "rates.CAD.rates", "data_type": "object", "is_categorical": false, "description": "", "nested_fields": [{"name": "rates.CAD.rates.NGN", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "rates.CAD.rates.USD", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "rates.CAD.rates.GHS", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "rates.CAD.rates.GBP", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "rates.CAD.rates.EUR", "data_type": "float", "is_categorical": false, "description": ""}, {"name": "rates.CAD.rates.KES", "data_type": "integer", "is_categorical": false, "description": ""}, {"name": "rates.CAD.rates.UGX", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None", "2644.9416", "2640.870484", "2700"]}, {"name": "rates.CAD.rates.CAD", "data_type": "integer", "is_categorical": true, "description": "", "found_categorical_values": ["None", "1", "1"]}]}]}]}, {"name": "createdAt", "data_type": "string", "is_categorical": false, "description": "Timestamp indicating when the transaction was created."}, {"name": "updatedAt", "data_type": "string", "is_categorical": false, "description": "Timestamp indicating when the transaction was last updated."}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "Version key for the document, used for internal versioning.", "found_categorical_values": ["0", "None"]}, {"name": "currency", "data_type": "string", "is_categorical": true, "description": "The currency in which the transaction amount is denominated.", "found_categorical_values": ["USD", "NGN", "GBP", "CAD", "GHS", "KES", "UGX", "ngn", "BXC", "usd", "EUR"]}, {"name": "fulfillmentAsset", "data_type": "string", "is_categorical": true, "description": "The asset used to fulfill the transaction.", "found_categorical_values": ["NGN", "None", "USD", "GBP", "GHS", "CAD", "KES", "UGX", "btc"]}, {"name": "fulfillmentAssetAddress", "data_type": "string", "is_categorical": false, "description": ""}, {"name": "sourceCountry", "data_type": "string", "is_categorical": true, "description": "Country from which the transaction originated.", "found_categorical_values": ["US", "NG", "us", "GB", "CA", "None", "GH", "ng", "KE", "UG", "ca"]}, {"name": "state", "data_type": "string", "is_categorical": true, "description": "Current state of the transaction (e.g., pending, completed, failed).", "found_categorical_values": ["None", "SUCCESS", "Successful", "pending", "REFUNDED", "Pending", "Refunded", "Failed", "Processing", "PENDING", "failed", "Canceled", "Completed", "RETRY", "succeeded", "UNKNOWN", "successful", "PROCESSING"]}, {"name": "processor", "data_type": "string", "is_categorical": true, "description": "The payment processor handling the transaction.", "found_categorical_values": ["None", "STRIPE", "stripe", "providus", "PROVIDUS", "vfd", "zeepay", "mono", "binance", "beyonic", "mobile", "test_faucet", "flutter", "cellulant", "VFD"]}, {"name": "type", "data_type": "string", "is_categorical": true, "description": "The type of transaction (e.g., deposit, withdrawal, transfer).", "found_categorical_values": ["None", "Withdraw", "fiat-fiat-swap", "withdraw", "sell", "payout-to-card", "fiat-crypto-swap", "payout to card"]}, {"name": "type_of_transaction", "data_type": "string", "is_categorical": true, "description": "Specific type of transaction (e.g., buy, sell, exchange).", "found_categorical_values": ["sell-transactions", "card-charge", "transactions", "admin_credit", "swap", "transfer-fiat", "reversal", "bulk-payment", "agent-withdrawal", "banking-deposit", "agent-deposit", "admin_debit", "Deposit To Wallet", "swapping", "bank_transfer"]}, {"name": "__v", "data_type": "integer", "is_categorical": true, "description": "Version key for the document, used for internal versioning.", "found_categorical_values": ["0"]}], "sample_documents": "{\n  \"_id\": \"5f8d3e50d0a9545a6998f5ab\",\n  \"amountCrypto\": \"0\",\n  \"amount\": \"5.00\",\n  \"currency\": \"USD\",\n  \"type\": \"Deposit\",\n  \"fulfillmentAsset\": \"EUR\",\n  \"fulfillmentAssetValue\": \"750\",\n  \"payoutId\": \"5f8c3baf9ba1747d4882928\",\n  \"rate\": \"0\",\n  \"isChargeWithdrawal\": false,\n  \"processor\": \"xyz\",\n  \"user\": \"5f8c2e286a924945ec7f4abc\",\n  \"ledgerTransactionId\": \"5f8d3e32d0a9545a6998f5ac\",\n  \"reference\": \"Fakex-5f8c2e286a924945ec7f4abc-*************\",\n  \"state\": \"Pending\",\n  \"bank_account_info\": {\n    \"account_number\": \"**********\",\n    \"bank_name\": \"Sample Bank\",\n    \"account_name\": \"<PERSON>\",\n    \"phoneNumber\": \"\",\n    \"bank_code\": \"000123\"\n  },\n  \"customer_details\": {\n    \"name\": \"Sample User\",\n    \"email\": \"<EMAIL>\",\n    \"username\": \"sample123\",\n    \"photo\": \"\",\n    \"phone\": \"+***********\"\n  },\n  \"sourceCountry\": \"US\",\n  \"type_of_transaction\": \"buy-transactions\",\n  \"rates\": {\n    \"USD\": {\n      \"rates\": {\n        \"EUR\": 0.85,\n        \"GBP\": 0.75,\n        \"CAD\": 1.25,\n        \"AUD\": 1.35,\n        \"JPY\": 110,\n        \"CHF\": 0.92,\n        \"NZD\": 1.4,\n        \"USD\": 1\n      }\n    }\n  },\n  \"createdAt\": \"2022-01-15T10:30:45.603000\",\n  \"updatedAt\": \"2022-01-15T10:30:45.603000\",\n  \"__v\": 0\n}", "status": "deactivated"}]}
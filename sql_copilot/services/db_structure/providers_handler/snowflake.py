import json
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from enum import Enum
import uuid
from sql_copilot.services.db_structure.transformer import BaseSchemaTransformer
from sql_copilot.services.db_structure.providers import DatabaseProvider

class SnowflakeSchemaTransformer(BaseSchemaTransformer):
    """Transformer for Snowflake database schemas"""
    
    def __init__(self):
        super().__init__(DatabaseProvider.SNOWFLAKE)
    
    def validate_schema_data(self, raw_schema_data: List[Dict[str, Any]]) -> bool:
        """Validate Snowflake schema data format"""
        required_fields = ["schema_name", "table_name", "description", "fields"]
        
        for table_info in raw_schema_data:
            if not all(field in table_info for field in required_fields):
                return False
            
            # Validate fields structure
            for field in table_info.get("fields", []):
                field_required = ["name", "data_type", "description"]
                if not all(req in field for req in field_required):
                    return False
        
        return True
    
    def transform_to_json_db(self, raw_schema_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Transform Snowflake schema data to JSON database format"""
        if not self.validate_schema_data(raw_schema_data):
            raise ValueError("Invalid Snowflake schema data format")
        
        # Group tables by schema
        schemas_dict = {}
        
        for table_info in raw_schema_data:
            schema_name = table_info["schema_name"]
            
            if schema_name not in schemas_dict:
                schemas_dict[schema_name] = {"tables": []}
            
            schemas_dict[schema_name]["tables"].append(table_info)
        
        # Create the JSON database structure
        db_structure = {
            "provider": self.provider_name,
            "schemas": [],
            "tables": []
        }
        
        # Create schema records
        for schema_name, schema_data in schemas_dict.items():
            #skip INFORMATION_SCHEMA
            if schema_name == "INFORMATION_SCHEMA":
                continue
            table_descriptions = []
            schema_id = str(uuid.uuid4())
            for table in schema_data["tables"]:
                # Get categorical examples for this table
                '''categorical_examples = []
                for field in table.get("fields", []):
                    if field.get("is_categorical") and "found_categorical_values" in field:
                        string_values = [str(val) for val in field["found_categorical_values"]]
                        categorical_examples.append({
                            "field_name": field["name"],
                            "sample_values": string_values[:5]
                        })'''
                
                table_overview = {
                    "table_name": table["table_name"],
                    "description": table["description"]
                    #"categorical_examples": categorical_examples
                }
                table_descriptions.append(table_overview)
            
            schema_record = {
                "schema_id": schema_id,
                "schema_name": schema_name,
                "description": f"Snowflake schema containing {len(schema_data['tables'])} table(s)",
                "table_count": len(schema_data["tables"]),
                "tables_overview": table_descriptions
            }
            
            db_structure["schemas"].append(schema_record)
        
        # Create table records
        for table_info in raw_schema_data:
            processed_fields = []
            for field in table_info.get("fields", []):
                processed_field = {
                    "name": field["name"],
                    "data_type": field["data_type"],
                    "is_categorical": field.get("is_categorical", False),
                    "is_datetime": field.get("is_datetime", False),
                    "description": field["description"]
                }
                
                if field.get("is_categorical") and "found_categorical_values" in field:
                    string_values = [str(val) for val in field["found_categorical_values"]]
                    processed_field["categorical_values"] = string_values
                
                processed_fields.append(processed_field)
            
            table_record = {
                "table_id": str(uuid.uuid4()),
                "table_name": table_info["table_name"],
                "schema_id": table_info["schema_name"],
                "description": table_info["description"],
                "field_count": len(processed_fields),
                "fields": processed_fields
            }
            
            db_structure["tables"].append(table_record)
        
        
   
        return db_structure


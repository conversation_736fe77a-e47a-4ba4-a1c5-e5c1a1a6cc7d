import concurrent.futures
from typing import Dict, List, Any, Tuple, Optional
import time
import json
from decimal import Decimal
import datetime
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from pymongo import MongoClient
from langchain_core.prompts import ChatPromptTemplate
from utils import logger
from ...schema_generator.row_randomizer import generate_sample
from ...schema_generator.schema_analyzer import BaseSchemaAnalyzer,SchemaAnalyzerRegistry,SchemaAnalyzer
from config import Config
from bson import ObjectId
from ...schema_generator.categorical_detector import detect_categorical_fields

class Field(BaseModel):
    name: str
    description: str

class CollectionDescription(BaseModel):
    collection_name: str
    description: str
    fields: List[Field]

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        elif isinstance(obj, ObjectId):
            return str(obj)
        return super(CustomJSONEncoder, self).default(obj)

class MongoDBSchemaAnalyzer(BaseSchemaAnalyzer):
    """
    A class to analyze and generate schema documentation for MongoDB databases.
    
    This class provides functionality to:
    - Connect to a MongoDB database
    - Analyze collection structures and field properties
    - Generate AI-powered descriptions for collections and fields
    - Export the analysis results in JSON format
    """
    
    def __init__(self, connection_params: Dict[str, str], openai_api_key: Optional[str] = None):
        """
        Initialize the analyzer with MongoDB connection parameters.
        
        Args:
            connection_params: Dictionary containing MongoDB connection parameters:
                - username: MongoDB username
                - password: MongoDB password
                - cluster_url: MongoDB cluster URL
                - database: Database name
                - options: MongoDB connection options (optional)
            openai_api_key: Optional OpenAI API key for generating descriptions
        """
        
        
        self.connection_params = connection_params
        self.connection_params.setdefault('options', 'retryWrites=true&w=majority')
        self.openai_api_key = Config.OPENAI_API_KEY
        if not self.openai_api_key:
            raise ValueError("OpenAI API key must be provided either in constructor or as environment variable")
        logger.info("Initialized MongoDB Schema Analyzer")
    
    def _get_connection_string(self) -> str:
        """Construct and return the MongoDB connection string."""
        return f"mongodb+srv://{self.connection_params['username']}:{self.connection_params['password']}@{self.connection_params['cluster_url']}/{self.connection_params['database']}?{self.connection_params['options']}"

    def _get_mongo_client(self) -> MongoClient:
        """Create and return a MongoDB client."""
        connection_string = self._get_connection_string()
        return MongoClient(connection_string)

    def _generate_collection_description(self, collection_name: str, fields: List[str]) -> Dict:
        """Generate AI-powered descriptions for collections and fields."""
        model = ChatOpenAI(
            model="gpt-4o-mini",
            api_key=self.openai_api_key,
            temperature=0.2,
            max_tokens=1024
        )
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", "You are a database expert who provides clear, concise descriptions of MongoDB collections and fields."),
            ("user", f"""For the MongoDB collection '{collection_name}', generate:
                 1. A brief description of the table's purpose and function. IMPORTANT: In this collection description,describe the collection  explicitly list all the fields contained in this table by saying "This table contains the following fields: [list all field names]". Then proceed with the table's purpose description.
                2. Short, clear descriptions for each field: {', '.join(fields)}""")
        ])
        
        chain = prompt | model.with_structured_output(CollectionDescription)
        response = chain.invoke({"messages": []})
        return {"collection_name": response.collection_name, "description": response.description, "fields": response.fields}

    def _analyze_field_type(self, value: Any) -> str:
        """Determine the type of a MongoDB field value."""
        if isinstance(value, dict):
            return "object"
        elif isinstance(value, list):
            return "array"
        elif isinstance(value, str):
            return "string"
        elif isinstance(value, int):
            return "integer"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, bool):
            return "boolean"
        elif isinstance(value, datetime.datetime):
            return "date"
        elif value is None:
            return "null"
        else:
            return str(type(value).__name__)

    def _is_categorical_field(self, collection, field_name: str, sample_size: int = 1000, max_unique_ratio: float = 0.1) -> Tuple[bool, List[Any]]:
        """
        Determine if a field is categorical based on AI analysis.
        
        Args:
            collection: MongoDB collection object
            field_name: Name of the field to analyze
            sample_size: Number of documents to sample for analysis
            max_unique_ratio: Maximum ratio of unique values to total values to be considered categorical
            
        Returns:
            Tuple of (is_categorical: bool, categorical_values: List[Any])
        """
        try:
            # Get total document count
            total_count = collection.count_documents({})
            
            # If collection is empty, return False
            if total_count == 0:
                return False, []
            
            # Determine actual sample size (min of total_count and sample_size)
            actual_sample_size = min(total_count, sample_size)
            
            # Get distinct values for the field from the sample
            pipeline = [
                {"$sample": {"size": actual_sample_size}},
                {"$group": {"_id": f"${field_name}", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}}
            ]
            
            distinct_values = list(collection.aggregate(pipeline))
            
            # Count unique values
            unique_count = len(distinct_values)
            
            # Prepare sample for AI detection
            sample = {
                "name": field_name,
                "data_type": self._analyze_field_type(distinct_values[0]["_id"]) if distinct_values else "unknown",
                "description": "",  # Will be filled later with AI-generated description
                "unique_count": unique_count,
                "total_count": actual_sample_size,
                "unique_ratio": unique_count / actual_sample_size if actual_sample_size > 0 else 0,
                "sample_values": [str(item["_id"]) for item in distinct_values[:10]]
            }
            
            # Use AI to detect if field is categorical
            ai_result = detect_categorical_fields(sample)
            is_categorical = ai_result.strip().upper() == "YES"
            
            # If categorical, get the values and their counts
            categorical_values = [
                str(item["_id"])
                for item in distinct_values[:50]
            ] if is_categorical else []
            
            return is_categorical, categorical_values
            
        except Exception as e:
            logger.error(f"Error analyzing categorical field {field_name}: {str(e)}")
            return False, []

    def _analyze_document_schema(self, document: Dict, prefix: str = "", collection=None) -> List[Dict]:
        """Recursively analyze the schema of a MongoDB document."""
        fields = []
        for key, value in document.items():
            field_name = f"{prefix}{key}" if prefix else key
            field_type = self._analyze_field_type(value)
            
            field_info = {
                "name": field_name,
                "data_type": field_type,
                "is_categorical": False,
                "description": ""
            }
            
            # Check if field is categorical if collection is provided and field is a basic type
            if collection is not None and field_type in ["string", "integer"]:
                is_categorical, categorical_values = self._is_categorical_field(collection, field_name)
                if is_categorical:
                    field_info["is_categorical"] = True
                    field_info["found_categorical_values"] = categorical_values
            
            if field_type == "object":
                nested_fields = self._analyze_document_schema(value, f"{field_name}.", collection)
                field_info["nested_fields"] = nested_fields
            elif field_type == "array" and value and isinstance(value[0], dict):
                sample_item = value[0]
                nested_fields = self._analyze_document_schema(sample_item, f"{field_name}[].", collection)
                field_info["array_items"] = nested_fields
            
            fields.append(field_info)
        
        return fields

    def _count_missing_values(self, document: Dict) -> int:
        """Count the number of missing (None or empty) values in a document recursively."""
        count = 0
        for value in document.values():
            if value is None:
                count += 1
            elif isinstance(value, dict):
                count += self._count_missing_values(value)
            elif isinstance(value, list) and value and isinstance(value[0], dict):
                # For array of objects, check the first item
                count += self._count_missing_values(value[0])
        return count

    def _analyze_collection(self, db_name: str, collection_name: str) -> Dict:
        """Analyze a single collection and return its metadata."""
        logger.info(f"Starting analysis of collection: {collection_name}")
        
        collection_info = {
            "database_name": db_name,
            "collection_name": collection_name,
            "description": "",
            "fields": [],
            "sample_documents": [],
            "status": "deactivated"
        }
        
        try:
            client = self._get_mongo_client()
            db = client[db_name]
            collection = db[collection_name]
            
            # Get sample documents and sort by number of missing values
            sample_docs = list(collection.find().limit(10))  # Get more samples initially
            if sample_docs:
                # Convert to JSON-serializable format
                sample_docs = json.loads(json.dumps(sample_docs, cls=CustomJSONEncoder))
                
                # Sort documents by number of missing values
                sample_docs.sort(key=lambda doc: self._count_missing_values(doc))
                
                # Take the two documents with least missing values
                best_samples = sample_docs[:2]
                
                # Generate a sample based on the document with least missing values
                sample_doc = generate_sample(sample=best_samples[0])
                collection_info["sample_documents"] = sample_doc
                
                # Analyze schema from sample documents with collection reference for categorical analysis
                merged_fields = []
                for doc in best_samples:
                    fields = self._analyze_document_schema(doc, collection=collection)
                    for field in fields:
                        if field not in merged_fields:
                            merged_fields.append(field)
                
                # Generate AI descriptions
                field_names = [field["name"] for field in merged_fields]
                try:
                    ai_descriptions = self._generate_collection_description(collection_name, field_names)
                    collection_info["description"] = ai_descriptions["description"]
                    
                    # Map descriptions to fields
                    field_descriptions = {field.name: field.description for field in ai_descriptions["fields"]}
                    for field in merged_fields:
                        field["description"] = field_descriptions.get(field["name"], "")
                except Exception as e:
                    logger.error(f"Error generating AI descriptions for {collection_name}: {e}")
                
                collection_info["fields"] = merged_fields
            
        except Exception as e:
            logger.error(f"Error analyzing collection {collection_name}: {str(e)}")
            collection_info["error"] = str(e)
        
        return collection_info

    def test_connection(self) -> Tuple[bool, Optional[str]]:
        """
        Test if the connection parameters are valid by attempting to connect to MongoDB.
        
        Returns:
            Tuple containing:
            - Boolean indicating if connection was successful
            - Optional error message (None if connection successful)
        """
        try:
            # Get MongoDB client
            client = self._get_mongo_client()
            
            # Try to access the database and list collections to verify connection
            db = client[self.connection_params['database']]
            db.list_collection_names()
            
            # Close the connection
            client.close()
            
            logger.info("Successfully tested connection to MongoDB")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to connect to MongoDB: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def analyze_schema(self,max_workers: int = 10) -> Tuple[List[Dict], Dict, Optional[str]]:
        """
        Analyze a MongoDB database and generate comprehensive documentation.
        
        Args:
            database: Name of the database to analyze
            max_workers: Maximum number of parallel workers for analysis
            
        Returns:
            Tuple containing:
            - List of dictionaries containing collection information and analysis
            - Dictionary containing analysis report
            - Optional error string (None if no errors)
        """
        try:
            database = self.connection_params["database"]
            start_time = time.time()
            
            # Initialize report statistics
            report = {
                "database": database,
                "total_collections": 0,
                "successfully_analyzed": 0,
                "failed_analysis": 0,
                "failed_collections": [],
                "execution_time": 0
            }
            
            client = self._get_mongo_client()
            db = client[database]
            
            # Get all collections in the database
            collections = db.list_collection_names()
            report["total_collections"] = len(collections)
            
            logger.info(f"Starting parallel collection analysis for database: {database}")
            
            all_results = []
            
            # Process collections in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_collection = {
                    executor.submit(self._analyze_collection, database, collection_name): collection_name 
                    for collection_name in collections
                }
                
                for future in concurrent.futures.as_completed(future_to_collection):
                    collection_name = future_to_collection[future]
                    try:
                        collection_info = future.result()
                        all_results.append(collection_info)
                        report["successfully_analyzed"] += 1
                        logger.info(f"Completed analysis for collection: {collection_name}")
                    except Exception as exc:
                        logger.error(f"Error processing collection {collection_name}: {exc}")
                        report["failed_analysis"] += 1
                        report["failed_collections"].append(collection_name)
                        all_results.append({
                            "database_name": database,
                            "collection_name": collection_name,
                            "error": str(exc)
                        })
            
            elapsed_time = time.time() - start_time
            report["execution_time"] = round(elapsed_time, 2)
            
            logger.info("\nAnalysis Report:")
            logger.info(f"Database analyzed: {database}")
            logger.info(f"Total collections: {report['total_collections']}")
            logger.info(f"Successfully analyzed: {report['successfully_analyzed']}")
            logger.info(f"Failed analysis: {report['failed_analysis']}")
            logger.info(f"Execution time: {report['execution_time']} seconds")
            
            return all_results, report, None
            
        except Exception as e:
            error_msg = f"Error during schema analysis: {str(e)}"
            logger.error(error_msg)
            return [], report, error_msg

    def export_to_json(self, result_list: List[Dict], report: Dict, output_file: str = 'mongodb_schema_analysis.json'):
        """
        Export the analysis results and report to a JSON file.
        
        Args:
            result_list: Analysis results to export
            report: Analysis report to include
            output_file: Path to the output JSON file
        """
        output = {
            "report": report,
            "collections": result_list
        }
        
        with open(output_file, 'w') as json_file:
            json.dump(output, json_file, indent=2, cls=CustomJSONEncoder)

# Register the mongodb analyzer
SchemaAnalyzerRegistry.register('mongodb', MongoDBSchemaAnalyzer) 
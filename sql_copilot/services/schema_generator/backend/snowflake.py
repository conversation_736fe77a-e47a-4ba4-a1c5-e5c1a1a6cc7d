import concurrent.futures
from typing import Dict, List, Any, Tuple, Optional
import time
import json
from decimal import Decimal
import datetime
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
import snowflake.connector
from langchain_core.prompts import ChatPromptTemplate
from utils import logger
from ..row_randomizer import generate_sample
from ..schema_analyzer import BaseSchemaAnalyzer,SchemaAnalyzerRegistry
from config import Config
from ..categorical_detector import detect_categorical_fields
class Field(BaseModel):
    name: str
    description: str

class TableDescription(BaseModel):
    table_name: str
    description: str
    fields: List[Field]

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        return super(CustomJSONEncoder, self).default(obj)



class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        return super(CustomJSONEncoder, self).default(obj)


class SnowflakeSchemaAnalyzer(BaseSchemaAnalyzer):
    """
    A class to analyze and generate schema documentation for Snowflake databases.
    
    This class provides functionality to:
    - Connect to a Snowflake database
    - Analyze table structures and column properties
    - Generate AI-powered descriptions for tables and columns
    - Export the analysis results in JSON format
    """
    
    def __init__(self, connection_params: Dict[str, str], openai_api_key: Optional[str] = None):
        """
        Initialize the analyzer with connection parameters.
        
        Args:
            connection_params: Dict containing Snowflake connection parameters:
                - user: Snowflake username
                - password: Snowflake password
                - account: Snowflake account identifier
                - role: Snowflake role
                - database: Database name
                - warehouse: Warehouse name
            openai_api_key: Optional OpenAI API key for generating descriptions
        """
        # Remove schema from connection params if it exists
        if 'schema' in connection_params:
            del connection_params['schema']
        self.connections = connection_params
        self.openai_api_key = Config.OPENAI_API_KEY
        if not self.openai_api_key:
            raise ValueError("OpenAI API key must be provided either in constructor or as environment variable")
        logger.info("Initialized Snowflake Schema Analyzer")
    
    def test_connection(self) -> Tuple[bool, Optional[str]]:
        """
        Test if the connection parameters are valid by attempting to connect to Snowflake.
        
        Returns:
            Tuple containing:
            - Boolean indicating if connection was successful
            - Optional error message (None if connection successful)
        """
        try:
            # Attempt to establish a connection
            connection = snowflake.connector.connect(**self.connections)
            
            # Try executing a simple query to verify connection
            cursor = connection.cursor()
            cursor.execute("SELECT CURRENT_TIMESTAMP()")
            cursor.fetchone()
            
            # Close the connection
            cursor.close()
            connection.close()
            
            logger.info("Successfully tested connection to Snowflake")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to connect to Snowflake: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def _execute_sql(self, query: str, schema: Optional[str] = None) -> Tuple[Optional[List[Any]], Optional[str]]:
        """Execute SQL query and return results."""
        try:
            conn_params = self.connections.copy()
            if schema:
                conn_params['schema'] = schema
            connection = snowflake.connector.connect(**conn_params)
            cursor = connection.cursor()
            result = cursor.execute(query)
            return result.fetchall(), None
        except Exception as error:
            return None, str(error)
        finally:
            if 'connection' in locals():
                connection.close()

    def _generate_table_description(self, table_name: str, fields: List[str]) -> Dict:
        """Generate AI-powered descriptions for tables and fields."""
        model = ChatOpenAI(
            model="gpt-4o-mini",
            api_key=self.openai_api_key,
            temperature=0.2,
            max_tokens=1024
        )
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", "You are a database expert who provides clear, concise descriptions of database tables and fields."),
            ("user", f"""For the database table '{table_name}', generate:
                1. A brief description of the table's purpose and function. IMPORTANT: In this table description,describe the table  explicitly list all the fields contained in this table by saying "This table contains the following fields: [list all field names]". Then proceed with the table's purpose description.
                2. Short, clear descriptions for each field: {', '.join(fields)}
            """)
        ])
        
        chain = prompt | model.with_structured_output(TableDescription)
        response = chain.invoke({"messages": []})
        return {"table_name": response.table_name, "description": response.description, "fields": response.fields}

    def analyze_schema(self,max_workers: int = 10,**kwargs) -> Tuple[List[Dict], Dict, Optional[str]]:
        """
        Analyze one or more database schemas and generate comprehensive documentation.
        
        Args:
            schemas: Optional list of schema names to analyze. If None, analyzes all schemas.
            max_workers: Maximum number of parallel workers for analysis
            
        Returns:
            Tuple containing:
            - List of dictionaries containing table information and analysis
            - Dictionary containing analysis report
            - Optional error string (None if no errors)
        """
        try:
            start_time = time.time()
            
            # Initialize report statistics
            report = {
                "total_schemas": 0,
                "analyzed_schemas": [],
                "total_tables_found": 0,
                "successfully_analyzed": 0,
                "failed_analysis": 0,
                "failed_tables": [],
                "execution_time": 0
            }
            
            # If no schemas specified, get all schemas from the database
            #check if cheams are presnt in kwargs
            if "schemas" in kwargs:
                schemas = kwargs["schemas"]
            else:
                schemas = None

            if schemas is None:
                schemas_query = f"""
                SELECT DISTINCT SCHEMA_NAME 
                FROM {self.connections['database']}.INFORMATION_SCHEMA.SCHEMATA
                """
                schemas_result, error = self._execute_sql(query=schemas_query)
                if error:
                    logger.error(f"Error fetching schemas: {error}")
                    return [], report, f"Failed to fetch schemas: {error}"
                
                schemas = [schema[0] for schema in schemas_result]
                #remove INFORMATION_SCHEMA
                schemas = [schema for schema in schemas if schema != "INFORMATION_SCHEMA"]
            
            report["total_schemas"] = len(schemas)
            logger.info(f"Starting parallel schema analysis for schemas: {', '.join(schemas)}")
            
            all_results = []
            
            def process_schema(schema: str) -> Tuple[List[Dict], Dict]:
                """Process a single schema and return its results and stats"""
                logger.info(f"\nAnalyzing schema: {schema}")
                
                schema_stats = {
                    "schema_name": schema,
                    "tables_count": 0,
                    "successful_tables": 0,
                    "failed_tables": 0
                }
                
                tables_query = f"""
                SELECT 
                    TABLE_NAME, 
                    COLUMN_NAME, 
                    DATA_TYPE,
                    ORDINAL_POSITION
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = '{schema}'
                ORDER BY TABLE_NAME, ORDINAL_POSITION;
                """
                
                tables_result, error = self._execute_sql(query=tables_query, schema=schema)
                if error:
                    logger.error(f"Error fetching schema {schema}: {error}")
                    return [], schema_stats
                
                # Group columns by table
                tables = {}
                for table_name, column_name, data_type, position in tables_result:
                    if table_name not in tables:
                        tables[table_name] = []
                    tables[table_name].append({
                        "name": column_name,
                        "data_type": data_type,
                        "position": position,
                        "is_categorical": False,
                        "description": ""
                    })
                
                schema_stats["tables_count"] = len(tables)
                schema_results = []
                
                # Process tables within this schema concurrently
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_table = {
                        executor.submit(self._analyze_table, table_name, columns, schema): table_name 
                        for table_name, columns in tables.items()
                    }
                    
                    for future in concurrent.futures.as_completed(future_to_table):
                        table_name = future_to_table[future]
                        try:
                            table_info = future.result()
                            table_info["schema_name"] = schema
                            schema_results.append(table_info)
                            schema_stats["successful_tables"] += 1
                            logger.info(f"Completed analysis for table: {schema}.{table_name}")
                        except Exception as exc:
                            logger.error(f"Error processing table {schema}.{table_name}: {exc}")
                            schema_stats["failed_tables"] += 1
                            schema_results.append({
                                "schema_name": schema,
                                "table_name": table_name,
                                "description": "",
                                "fields": [],
                                "sample_rows": [],
                                "error": str(exc)
                            })
                
                return schema_results, schema_stats
            
            # Process schemas in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as schema_executor:
                future_to_schema = {
                    schema_executor.submit(process_schema, schema): schema 
                    for schema in schemas
                }
                
                for future in concurrent.futures.as_completed(future_to_schema):
                    schema = future_to_schema[future]
                    try:
                        schema_results, schema_stats = future.result()
                        all_results.extend(schema_results)
                        report["analyzed_schemas"].append(schema_stats)
                        report["total_tables_found"] += schema_stats["tables_count"]
                        report["successfully_analyzed"] += schema_stats["successful_tables"]
                        report["failed_analysis"] += schema_stats["failed_tables"]
                    except Exception as exc:
                        logger.error(f"Error processing schema {schema}: {exc}")
                        report["analyzed_schemas"].append({
                            "schema_name": schema,
                            "tables_count": 0,
                            "successful_tables": 0,
                            "failed_tables": 0,
                            "error": str(exc)
                        })
            
            elapsed_time = time.time() - start_time
            report["execution_time"] = round(elapsed_time, 2)
            
            logger.info("\nAnalysis Report:")
            logger.info(f"Total schemas analyzed: {report['total_schemas']}")
            logger.info(f"Total tables found: {report['total_tables_found']}")
            logger.info(f"Successfully analyzed: {report['successfully_analyzed']}")
            logger.info(f"Failed analysis: {report['failed_analysis']}")
            logger.info(f"Execution time: {report['execution_time']} seconds")

            return all_results, report, None

        except Exception as e:
            error_msg = f"Error during schema analysis: {str(e)}"
            logger.error(error_msg)
            return [], report, error_msg

    def _analyze_table(self, table_name: str, columns: List[Dict], schema: str) -> Dict:
        """Analyze a single table and return its metadata in the specified format"""
        logger.info(f"Starting analysis of table: {schema}.{table_name}")
        
        # Initialize the table structure first
        table_info = {
            "schema_name": schema,
            "table_name": f"{schema}.{table_name}",
            "description": "",
            "fields": [],
            "status": "deactivated",
            "sample_rows": []
        }
        
        try:
            # Get field names for AI description generation
            field_names = [col["name"] for col in columns]
            
            # Generate AI descriptions for table and fields
            try:
                ai_descriptions = self._generate_table_description(table_name, field_names)
                table_info["description"] = ai_descriptions["description"]
                
                # Create a mapping of field names to their descriptions
                field_descriptions = {field.name: field.description for field in ai_descriptions["fields"]}
            except Exception as e:
                logger.error(f"Error generating AI descriptions for {table_name}: {e}")
                field_descriptions = {}
            
            # Get column names for the table (needed for sample rows)
            column_names = [col["name"] for col in columns]
            
            # Simplified query to get rows with least NULL values
            sample_rows_query = f"""
            WITH row_stats AS (
                SELECT *,
                       {' + '.join(f'CASE WHEN "{col["name"]}" IS NOT NULL THEN 1 ELSE 0 END' for col in columns)} as non_null_count
                FROM "{schema}"."{table_name}"
            )
            SELECT {', '.join(f'"{col["name"]}"' for col in columns)}
            FROM row_stats
            WHERE non_null_count > 0  -- Ensure we get rows with at least some data
            ORDER BY non_null_count DESC, RANDOM()
            LIMIT 2
            """
            
            logger.debug(f"Executing sample rows query: {sample_rows_query}")
            
            sample_rows_result, sample_rows_error = self._execute_sql(
                query=sample_rows_query,
                schema=schema
            )
            
            # Format sample rows as JSON objects with better error handling
            json_sample_rows = []
            if not sample_rows_error and sample_rows_result:
                for row in sample_rows_result:
                    row_dict = {}
                    for i, value in enumerate(row):
                        if i < len(column_names):
                            column_name = column_names[i]
                            row_dict[column_name] = self._convert_to_json_serializable(value)
                    if row_dict:  # Only append if we have data
                        json_sample_rows.append(row_dict)
            
            if not json_sample_rows:
                # Fallback query - try to get any non-empty row
                fallback_query = f"""
                SELECT {', '.join(f'"{col["name"]}"' for col in columns)}
                FROM "{schema}"."{table_name}"
                WHERE ({' OR '.join(f'"{col["name"]}" IS NOT NULL' for col in columns)})
                LIMIT 2
                """
                
                logger.debug(f"Trying fallback query: {fallback_query}")
                fallback_result, fallback_error = self._execute_sql(
                    query=fallback_query,
                    schema=schema
                )
                
                if not fallback_error and fallback_result:
                    for row in fallback_result:
                        row_dict = {}
                        for i, value in enumerate(row):
                            if i < len(column_names):
                                column_name = column_names[i]
                                row_dict[column_name] = self._convert_to_json_serializable(value)
                        if row_dict:
                            json_sample_rows.append(row_dict)
            
            if json_sample_rows:  # Only generate sample if we have data
                sample_row = generate_sample(sample=json_sample_rows[0])
                table_info["sample_rows"] = sample_row
            else:
                logger.warning(f"No sample rows found for table {schema}.{table_name} after both attempts")
            
            # Simplified debug info
            table_info["debug_info"] = {
                "has_sample_rows": len(json_sample_rows) > 0,
                "sample_rows_error": str(sample_rows_error) if sample_rows_error else None
            }
            
            # Process fields with categorical analysis
            analyzed_fields = []
            for column in columns:
                # First check if it's a datetime
                is_datetime = column["data_type"].upper() in ("DATE", "DATETIME", "TIMESTAMP", "TIMESTAMP_NTZ", "TIMESTAMP_LTZ", "TIMESTAMP_TZ")
                
                field_info = {
                    "name": column["name"],
                    "data_type": column["data_type"],
                    "is_categorical": False,
                    "is_datetime": is_datetime,
                    "description": field_descriptions.get(column["name"], "")
                }
                
                # Handle different column types
                if column["data_type"].upper() == "VARIANT":
                    field_info = self._analyze_variant_column(table_name, column["name"], field_info)
                elif not is_datetime:  # Only analyze non-datetime fields for categorical values
                    field_info = self._analyze_regular_column(f"{schema}.{table_name}", column["name"], column["data_type"], field_info)
                
                analyzed_fields.append(field_info)
            
            table_info["fields"] = analyzed_fields
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {str(e)}")
            table_info["error"] = str(e)
        
        return table_info

    def _analyze_column(self, table_name: str, column_name: str, data_type: str) -> Dict[str, Any]:
        """Analyze a single column and return basic metadata"""
        column_info = {
            "name": column_name,
            "data_type": data_type,
            "is_categorical": False,
            "is_datetime": False,  # Added back is_datetime
            "description": ""  # Will be filled later with AI-generated description
        }
        
        try:
            # Check for date/time types first
            if data_type.upper() in ("DATE", "DATETIME", "TIMESTAMP", "TIMESTAMP_NTZ", "TIMESTAMP_LTZ", "TIMESTAMP_TZ"):
                column_info["is_datetime"] = True
                return column_info  # Return early for datetime fields - no need to check if categorical
            
            # Handle VARIANT data type
            if data_type.upper() == "VARIANT":
                column_info = self._analyze_variant_column(table_name, column_name, column_info)
            else:
                # Handle regular columns
                column_info = self._analyze_regular_column(table_name, column_name, data_type, column_info)
        except Exception as e:
            logger.error(f"Error analyzing column {column_name}: {e}")
        
        return column_info

    def _analyze_variant_column(self, table_name: str, column_name: str, column_info: Dict) -> Dict:
        """Analyze a VARIANT column to determine its structure"""
        column_info["variant"] = "json_variant"

        # Check the specific type of variant
        sample_query = f"""
        SELECT TOP 1 
            CASE 
                WHEN IS_OBJECT("{column_name}") THEN 'json_object'
                WHEN IS_ARRAY("{column_name}") THEN 'json_array'
                ELSE 'other_variant'
            END as variant_type,
            "{column_name}"
        FROM "{table_name}"
        WHERE "{column_name}" IS NOT NULL
        """

        sample_result, sample_error = self._execute_sql(query=sample_query)

        if not sample_error and sample_result and len(sample_result) > 0:
            variant_type = sample_result[0][0]
            column_info["variant"] = variant_type

            if variant_type == 'json_object':
                # Get keys from the JSON object
                key_query = f"""
                SELECT ARRAY_AGG(DISTINCT key) as keys
                FROM {table_name},
                LATERAL FLATTEN(input => "{column_name}")
                """
                key_result, key_error = self._execute_sql(query=key_query)

                if not key_error and key_result and len(key_result) > 0 and key_result[0][0]:
                    column_info["json_keys"] = key_result[0][0]
       

        return column_info

    def _analyze_regular_column(self, table_name: str, column_name: str, data_type: str, column_info: Dict) -> Dict:
        """Analyze a regular column for categorical values"""
        
        # Skip floating point numbers
        if data_type.upper() in ("FLOAT", "DOUBLE", "REAL", "DECIMAL", "NUMERIC"):
            return column_info
        
        try:
            # Get unique value counts
            stats_query = f"""
            SELECT 
                COUNT(DISTINCT "{column_name}") as unique_count,
                COUNT(*) as total_count
            FROM {table_name}
            WHERE "{column_name}" IS NOT NULL
            """
            
            stats_result, stats_error = self._execute_sql(query=stats_query)
            
            if not stats_error and stats_result and stats_result[0][0] is not None:
                unique_count, total_count = stats_result[0]
                
                # Get sample values for AI-based categorical detection
                if total_count > 0:
                    values_query = f"""
                    SELECT DISTINCT "{column_name}" as value
                    FROM {table_name}
                    WHERE "{column_name}" IS NOT NULL
                    ORDER BY value
                    LIMIT 10
                    """
                    
                    values_result, values_error = self._execute_sql(query=values_query)
                    
                    if not values_error and values_result:
                        # Prepare sample for AI detection
                        sample = {
                            "name": column_name,
                            "data_type": data_type,
                            "description": column_info.get("description", ""),  # Include existing AI-generated description
                            "unique_count": unique_count,
                            "total_count": total_count,
                            "unique_ratio": unique_count / total_count if total_count > 0 else 0,
                            "sample_values": [self._convert_to_json_serializable(row[0]) for row in values_result]
                        }
                        
                        # Use AI to detect if field is categorical
                        ai_result = detect_categorical_fields(sample)
                        is_categorical = ai_result.strip().upper() == "YES"

                        print(f"Table Name: {table_name}, Column Name: {column_name}, Is Categorical: {is_categorical}")
                        
                        column_info["is_categorical"] = is_categorical
                        
                        # If categorical, get the distinct values
                        if is_categorical:
                            # Get all values if we need more than the sample
                            if unique_count > 10:
                                full_values_query = f"""
                                SELECT DISTINCT "{column_name}" as value
                                FROM {table_name}
                                WHERE "{column_name}" IS NOT NULL
                                ORDER BY value
                                LIMIT 50
                                """
                                
                                full_values_result, full_values_error = self._execute_sql(query=full_values_query)
                                
                                if not full_values_error and full_values_result:
                                    found_categorical_values = [
                                        self._convert_to_json_serializable(row[0]) 
                                        for row in full_values_result
                                    ]
                                    column_info["found_categorical_values"] = found_categorical_values
                            else:
                                # Use the sample values we already have
                                column_info["found_categorical_values"] = sample["sample_values"]
        
        except Exception as e:
            logger.error(f"Error analyzing column {column_name} for categorical values: {str(e)}")
        
        return column_info

    
    def _convert_to_json_serializable(self, obj):
        """Convert an object to a JSON-serializable type"""
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        else:
            return obj

    def export_to_json(self, result_list: List[Dict], report: Dict, output_file: str = 'schema_analysis.json'):
        """
        Export the analysis results and report to a JSON file.
        
        Args:
            result_list: Analysis results to export
            report: Analysis report to include
            output_file: Path to the output JSON file
        """
        output = {
            "report": report,
            "tables": result_list  # Changed from nested data structure to direct table list
        }
        
        with open(output_file, 'w') as json_file:
            json.dump(output, json_file, indent=2, cls=CustomJSONEncoder)

    
# Register the mongodb analyzer
SchemaAnalyzerRegistry.register('snowflake', SnowflakeSchemaAnalyzer) 

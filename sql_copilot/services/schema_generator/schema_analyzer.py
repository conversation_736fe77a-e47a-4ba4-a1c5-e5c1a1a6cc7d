import logging
from typing import Dict, List, Any, Optional, Type
from abc import ABC, abstractmethod
from utils import logger


class BaseSchemaAnalyzer(ABC):
    """Abstract base class for all database schema analyzers"""
    
    @abstractmethod
    def analyze_schema(self,max_workers: int = 10,**kwargs) -> tuple[List[Dict], Dict]:
        """
        Analyze database schemas and generate documentation.
        
        Args:
            schemas: Optional list of schema names to analyze
            max_workers: Maximum number of parallel workers
            
        Returns:
            Tuple containing:
            - List of dictionaries containing table information
            - Dictionary containing analysis report
        """
        pass
    
    @abstractmethod
    def test_connection(self) -> tuple[bool, Optional[str]]:
        """
        Test if the connection parameters are valid.
        
        Returns:
            Tuple containing:
            - <PERSON><PERSON><PERSON> indicating if connection was successful
            - Optional error message (None if connection successful)
        """
        pass
    
    @abstractmethod
    def export_to_json(self, result_list: List[Dict], report: Dict, output_file: str) -> None:
        """
        Export analysis results to JSON file.
        
        Args:
            result_list: Analysis results to export
            report: Analysis report to include
            output_file: Path to output JSON file
        """
        pass

class SchemaAnalyzerRegistry:
    """Registry for database schema analyzers"""
    
    _analyzers: Dict[str, Type[BaseSchemaAnalyzer]] = {}
    
    @classmethod
    def register(cls, backend_name: str, analyzer_class: Type[BaseSchemaAnalyzer]) -> None:
        """
        Register a new schema analyzer.
        
        Args:
            backend_name: Name of the database backend
            analyzer_class: Schema analyzer class to register
        """
        cls._analyzers[backend_name.lower()] = analyzer_class
        logger.info(f"Registered schema analyzer for {backend_name}")
    
    @classmethod
    def get_analyzer(cls, backend_name: str) -> Optional[Type[BaseSchemaAnalyzer]]:
        """
        Get registered analyzer class for a backend.
        
        Args:
            backend_name: Name of the database backend
            
        Returns:
            Registered analyzer class or None if not found
        """
        return cls._analyzers.get(backend_name.lower())
    
    @classmethod
    def list_supported_backends(cls) -> List[str]:
        """Get list of supported database backends"""
        return list(cls._analyzers.keys())


class SchemaAnalyzer:
    """
    Main schema analyzer class that delegates to specific backend implementations.
    
    This class serves as the entry point for schema analysis, handling:
    - Backend selection and initialization
    - Connection management
    - Analysis execution
    - Result export
    """
    
    def __init__(self, backend: str, connection_params: Dict[str, Any], openai_api_key: Optional[str] = None,**kwargs):
        """
        Initialize schema analyzer.
        
        Args:
            backend: Database backend name (e.g., 'snowflake', 'postgresql', 'mongodb')
            connection_params: Database connection parameters
            openai_api_key: Optional OpenAI API key for AI-powered descriptions
        """
        self.backend = backend.lower()
        self.connection_params = connection_params
        self.openai_api_key = openai_api_key
        self.kwargs = kwargs
        
        # Get appropriate analyzer class
        analyzer_class = SchemaAnalyzerRegistry.get_analyzer(self.backend)
        if not analyzer_class:
            supported = SchemaAnalyzerRegistry.list_supported_backends()
            raise ValueError(
                f"Unsupported database backend: {backend}. "
                f"Supported backends are: {', '.join(supported)}"
            )
        
        # Initialize backend-specific analyzer
        logger.info(f"Initializing {backend} schema analyzer")
        self.analyzer = analyzer_class(connection_params, openai_api_key)
    
    def analyze_schema(self, max_workers: int = 10,**kwargs) -> tuple[List[Dict], Dict]:
        """
        Analyze database schemas using the configured backend.
        
        Args:
            schemas: Optional list of schema names to analyze
            max_workers: Maximum number of parallel workers
            
        Returns:
            Tuple containing:
            - List of dictionaries containing table information
            - Dictionary containing analysis report
        """
        logger.info(f"Starting schema analysis for {self.backend} database")
        
        
       
        results, report,error = self.analyzer.analyze_schema(max_workers,**kwargs)
        logger.info("Schema analysis completed successfully")
        return results, report,error
        
    
    def export_to_json(self, result_list: List[Dict], report: Dict, output_file: str = 'schema_analysis.json') -> None:
        """
        Export analysis results to JSON file.
        
        Args:
            result_list: Analysis results to export
            report: Analysis report to include
            output_file: Path to output JSON file
        """
        logger.info(f"Exporting analysis results to {output_file}")
        try:
            self.analyzer.export_to_json(result_list, report, output_file)
            logger.info("Results exported successfully")
        except Exception as e:
            logger.error(f"Failed to export results: {str(e)}", exc_info=True)
            raise 

    def test_connection(self) -> tuple[bool, Optional[str]]:
        """
        Test if the connection parameters are valid using the configured backend.
        
        Returns:
            Tuple containing:
            - Boolean indicating if connection was successful
            - Optional error message (None if connection successful)
        """
        logger.info(f"Testing connection for {self.backend} database")
        try:
            is_connected, error = self.analyzer.test_connection()
            if is_connected:
                logger.info("Connection test successful")
            else:
                logger.error(f"Connection test failed: {error}")
            return is_connected, error
        except Exception as e:
            error_msg = f"Connection test failed with unexpected error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
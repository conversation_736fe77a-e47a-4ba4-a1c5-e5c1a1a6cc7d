
from dotenv import load_dotenv
import os
import logging
import json

def get_filtered_db(state: dict, DATABASE: dict, type: str = "sql") -> list:
    if type not in ["sql", "no_sql"]:
        raise ValueError("type must be 'sql' or 'no_sql'")

    if type == "sql":
        database_structure = [
                table for table in DATABASE if table["table_name"] in state["filtered_tables"]
            ]
    else:
        database_structure = [
                table for table in DATABASE if table["collection_name"] in state["filtered_tables"]
            ]
    return database_structure


def get_current_date() -> str:
    from datetime import datetime
    import pytz

    # Get current date and time in UTC
    utc_now = datetime.now(pytz.utc)
    # Format the current date and time in words
    return utc_now.strftime("%A, %B %d, %Y at %I:%M %p UTC")

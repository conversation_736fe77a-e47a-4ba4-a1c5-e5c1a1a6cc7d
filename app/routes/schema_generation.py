from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
import logging
import traceback
from config import Config
from .models.connection_params import (SnowflakeConnectionParams,
                                       MongoDBConnectionParams)
from sql_copilot.services.schema_generator.schema_analyzer import SchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mongodb import MongoDBSchemaAnalyzer
from sql_copilot.services.schema_generator.backend.snowflake import SnowflakeSchemaAnalyzer
import uuid
from celery_queue.tasks.schema_generator import generate_schema_task

# Expanded validator mapping to include more backends
VALIDATOR_MAPPING = {
    'snowflake': SnowflakeConnectionParams,
    "mongodb":MongoDBConnectionParams
}
class SchemaGenerationRequest(BaseModel):
    backend: str
    connection_params: Dict[str, Any]
    optional: Optional[Dict[str, Any]] = {}

    @validator('backend')
    def validate_backend(cls, v):
        """
        Validate that the backend is supported and provide clear error messages.
        """
        supported_backends = list(VALIDATOR_MAPPING.keys())
        if v.lower() not in supported_backends:
            raise ValueError(
                f"Unsupported backend '{v}'. "
                f"Supported backends are: {', '.join(supported_backends)}. "
                "Please choose a valid backend and provide corresponding connection parameters."
            )
        return v.lower()

    @validator('connection_params')
    def validate_connection_params(cls, v, values):
        """
        Validate connection parameters based on the specified backend.
        Provides detailed error messages about required parameters.
        """
        if 'backend' not in values:
            raise ValueError('Backend must be provided before connection parameters')
        
        backend = values['backend']
        validator_class = VALIDATOR_MAPPING.get(backend)
        
        if not validator_class:
            raise ValueError(f"No parameter validator found for backend: {backend}")
        
        try:
            # Validate and convert connection parameters
            validated_params = validator_class(**v).dict()
            return validated_params
        except ValueError as ve:
            # Enhance error message to specify backend-specific requirements
            raise ValueError(
                f"Invalid connection parameters for {backend} backend. "
                f"Error details: {str(ve)}. "
                f"Required parameters for {backend}: {list(validator_class.__fields__.keys())}"
            )

class QueryResponse(BaseModel):
    response: str
    results: List[Dict] = Field(default_factory=list)  # Provide a default empty dictionary
    report: Dict[str, Any] = Field(default_factory=dict)  # Provide a default empty dictionary
    error_detail: Optional[str] = None
    analytics_mode: bool = False

class SchemaGenerationResponse(BaseModel):
    request_id: str
    status: str
    message: str

class ConnectionTestResponse(BaseModel):
    success: bool
    message: str
    error_detail: Optional[str] = None

router = APIRouter(prefix="/api/v1/db-schema", tags=["schema generation API"])


@router.post("/generate", response_model=SchemaGenerationResponse)
async def generate_schema(request: SchemaGenerationRequest):
    try:
        # The SchemaGenerationRequest model will automatically validate
        # the backend and connection parameters through its validators

        # Generate a unique request ID
        request_id = str(uuid.uuid4())
        
        # Add debug logging
        logging.info(f"Initiating schema generation task with request_id: {request_id}")
        
        # Launch the background task
        task = generate_schema_task.delay(
            request_id=request_id,
            backend=request.backend,
            connection_params=request.connection_params,
            optional_params=request.optional
        )
        
        # Add task ID logging
        logging.info(f"Task initiated with task_id: {task.id}")
        
        return SchemaGenerationResponse(
            request_id=request_id,
            status="IN_PROGRESS",
            message="Schema generation started. Use request_id to check status."
        )

    except ValueError as ve:
        # Handle validation errors from the Pydantic model
        logging.error(f"Validation error: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logging.error(f"Error initiating schema generation: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to start schema generation: {str(e)}")
    


@router.post("/test-connection", response_model=ConnectionTestResponse)
async def test_connection(request: SchemaGenerationRequest):
    try:
        # Initialize the schema analyzer with the provided parameters
        analyzer = SchemaAnalyzer(
            backend=request.backend,
            connection_params=request.connection_params,
            openai_api_key=Config.OPENAI_API_KEY
        )
        
        # Test the connection
        is_connected, error = analyzer.test_connection()
        
        if is_connected:
            return ConnectionTestResponse(
                success=True,
                message="Successfully connected to database"
            )
        else:
            return ConnectionTestResponse(
                success=False,
                message="Failed to connect to database",
                error_detail=error
            )

    except Exception as e:
        logging.error(f"Error testing connection: {traceback.format_exc()}")
        return ConnectionTestResponse(
            success=False,
            message="Connection test failed",
            error_detail=str(e)
        )
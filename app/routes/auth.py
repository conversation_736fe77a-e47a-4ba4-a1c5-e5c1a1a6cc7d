from fastapi import APIRouter, HTTPException, Request
import requests
from config import Config


router = APIRouter(
    prefix="/auth",
    tags=["User authentication API"])

@router.get("/slack/oauth/callback")
async def oauth_callback(request: Request):
    code = request.query_params.get("code")
    if not code:
        return {"error": "Missing code parameter"}

    response = requests.post(
        "https://slack.com/api/oauth.v2.access",
        data={
            "client_id": Config.SLACK_CLIENT_ID,
            "client_secret": Config.SLACK_SECRET_ID,
            "code": code,
            "redirect_uri": Config.REDIRECT_URI
        }
    )
    data = response.json()
    if not data.get("ok"):
        return {"error": data.get("error")}

    access_token = data["access_token"]
    team_id = data["team"]["id"]
    team_name = data["team"]["name"]

    # Print access token, team ID, and team name
    print(f"Access Token: {access_token}, Team ID: {team_id}, Team Name: {team_name}")

    # TODO: Store access_token and team_id securely for future use

    return {"message": f"App installed to {team_name}!", "access_token": access_token, "team_id": team_id, "team_name": team_name}



    